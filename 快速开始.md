# 快速开始指南

## 问题解决

根据您的报错信息，主要是Pillow包在Python 3.13上的兼容性问题。我为您提供了几种解决方案：

## 方案1: 使用简化版程序（推荐）

### 1. 安装最少依赖
```bash
pip install -r requirements_simple.txt
```

### 2. 运行简化版程序
```bash
python netease_login_simple.py
```

这个版本只需要最基础的库，避免了复杂的依赖问题。

## 方案2: 使用依赖安装脚本

### 1. 运行自动安装脚本
```bash
python install_dependencies.py
```

这个脚本会自动处理各种兼容性问题，并提供备选安装方案。

### 2. 如果安装成功，运行完整版程序
```bash
python netease_login.py
```

## 方案3: 手动解决依赖问题

### 1. 升级pip
```bash
python -m pip install --upgrade pip
```

### 2. 安装兼容版本的Pillow
```bash
pip install Pillow>=10.1.0
```

### 3. 如果还是失败，尝试预编译版本
```bash
pip install --only-binary=all Pillow
```

### 4. 安装其他依赖
```bash
pip install requests selenium pycryptodome fake-useragent webdriver-manager
```

## 方案4: 使用conda环境（推荐Windows用户）

### 1. 安装Anaconda或Miniconda

### 2. 创建新环境
```bash
conda create -n netease python=3.11
conda activate netease
```

### 3. 安装依赖
```bash
conda install requests pillow opencv
pip install selenium ddddocr fake-useragent pycryptodome webdriver-manager
```

## 常见问题解决

### Q: Pillow安装失败
A: 尝试以下命令：
```bash
pip install --upgrade pip setuptools wheel
pip install Pillow --no-cache-dir
```

### Q: ddddocr安装失败
A: 这个库可选，可以跳过：
```bash
# 使用简化版程序，不需要ddddocr
python netease_login_simple.py
```

### Q: 所有包都安装失败
A: 使用国内镜像源：
```bash
pip install -r requirements_simple.txt -i https://pypi.douban.com/simple/
```

## 验证安装

运行以下命令检查安装：
```python
python -c "import requests; print('requests OK')"
python -c "import json; print('json OK')"
python -c "import hashlib; print('hashlib OK')"
```

## 使用说明

1. 确保 `账号密码` 文件格式正确：
```
13068503468
密码：7121020qing@
```

2. 运行程序：
```bash
# 简化版（推荐）
python netease_login_simple.py

# 完整版（如果依赖都安装成功）
python netease_login.py
```

3. 查看日志：
- 简化版日志：`netease_simple.log`
- 完整版日志：`netease_research.log`

## 注意事项

1. **仅用于学术研究** - 请勿用于商业或恶意目的
2. **账号安全** - 建议使用测试账号
3. **频率控制** - 避免过于频繁的请求
4. **法律合规** - 遵守相关法律法规

## 技术支持

如果仍有问题，请：
1. 查看生成的日志文件
2. 检查Python版本（建议3.8-3.11）
3. 尝试在虚拟环境中运行
4. 检查网络连接和防火墙设置
