"""
依赖安装脚本
解决Python 3.13兼容性问题
"""

import subprocess
import sys
import os


def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        print(f"执行命令: {command}")
        if result.stdout:
            print(f"输出: {result.stdout}")
        if result.stderr and result.returncode != 0:
            print(f"错误: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"命令执行异常: {e}")
        return False


def install_package(package_name, alternative_name=None):
    """安装单个包，支持备选方案"""
    print(f"\n正在安装 {package_name}...")
    
    # 尝试安装主包
    if run_command(f"pip install {package_name}"):
        print(f"✅ {package_name} 安装成功")
        return True
    
    # 如果有备选方案，尝试安装
    if alternative_name:
        print(f"主包安装失败，尝试备选方案: {alternative_name}")
        if run_command(f"pip install {alternative_name}"):
            print(f"✅ {alternative_name} 安装成功")
            return True
    
    print(f"❌ {package_name} 安装失败")
    return False


def main():
    """主安装流程"""
    print("=" * 60)
    print("网易云音乐登录绕过验证研究程序 - 依赖安装")
    print("=" * 60)
    
    # 升级pip
    print("\n1. 升级pip...")
    run_command("python -m pip install --upgrade pip")
    
    # 基础包列表（按安装顺序）
    packages = [
        ("requests", None),
        ("urllib3", None),
        ("beautifulsoup4", None),
        ("lxml", None),
        ("pycryptodome", "pycrypto"),
        ("fake-useragent", None),
        ("webdriver-manager", None),
        ("selenium", None),
    ]
    
    # 安装基础包
    print("\n2. 安装基础依赖包...")
    failed_packages = []
    
    for package, alternative in packages:
        if not install_package(package, alternative):
            failed_packages.append(package)
    
    # 特殊处理Pillow
    print("\n3. 安装图像处理库...")
    pillow_success = False
    pillow_versions = ["Pillow", "Pillow>=10.1.0", "Pillow>=9.0.0", "Pillow>=8.0.0"]
    
    for version in pillow_versions:
        if install_package(version):
            pillow_success = True
            break
    
    if not pillow_success:
        failed_packages.append("Pillow")
    
    # 特殊处理OpenCV
    print("\n4. 安装OpenCV...")
    opencv_success = False
    opencv_versions = [
        "opencv-python",
        "opencv-python-headless",
        "opencv-contrib-python"
    ]
    
    for version in opencv_versions:
        if install_package(version):
            opencv_success = True
            break
    
    if not opencv_success:
        failed_packages.append("opencv-python")
    
    # 特殊处理ddddocr（可能需要特殊处理）
    print("\n5. 安装验证码识别库...")
    ddddocr_success = False
    
    # 先尝试直接安装
    if install_package("ddddocr"):
        ddddocr_success = True
    else:
        # 尝试从其他源安装
        print("尝试从其他源安装ddddocr...")
        commands = [
            "pip install ddddocr -i https://pypi.douban.com/simple/",
            "pip install ddddocr --no-deps",
            "pip install ddddocr --force-reinstall"
        ]
        
        for cmd in commands:
            if run_command(cmd):
                ddddocr_success = True
                break
    
    if not ddddocr_success:
        failed_packages.append("ddddocr")
        print("⚠️  ddddocr安装失败，验证码识别功能可能受限")
    
    # 安装结果总结
    print("\n" + "=" * 60)
    print("安装结果总结")
    print("=" * 60)
    
    if not failed_packages:
        print("✅ 所有依赖包安装成功！")
        print("\n可以运行以下命令启动程序:")
        print("python netease_login.py")
    else:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 尝试使用不同的pip源")
        print("3. 考虑使用虚拟环境")
        
        # 提供手动安装命令
        print("\n手动安装命令:")
        for package in failed_packages:
            if package == "ddddocr":
                print(f"pip install {package} -i https://pypi.douban.com/simple/")
            else:
                print(f"pip install {package}")
    
    # 验证安装
    print("\n6. 验证安装...")
    test_imports = [
        ("requests", "requests"),
        ("selenium", "selenium"),
        ("PIL", "Pillow"),
        ("cv2", "opencv-python"),
        ("fake_useragent", "fake-useragent"),
        ("Crypto", "pycryptodome"),
        ("bs4", "beautifulsoup4"),
        ("lxml", "lxml"),
    ]
    
    working_imports = []
    failed_imports = []
    
    for import_name, package_name in test_imports:
        try:
            __import__(import_name)
            working_imports.append(package_name)
            print(f"✅ {package_name} 导入成功")
        except ImportError:
            failed_imports.append(package_name)
            print(f"❌ {package_name} 导入失败")
    
    # 特殊测试ddddocr
    try:
        import ddddocr
        working_imports.append("ddddocr")
        print("✅ ddddocr 导入成功")
    except ImportError:
        failed_imports.append("ddddocr")
        print("❌ ddddocr 导入失败")
    
    print(f"\n成功导入: {len(working_imports)} 个包")
    print(f"导入失败: {len(failed_imports)} 个包")
    
    if len(working_imports) >= 6:  # 至少6个核心包工作
        print("\n🎉 核心功能可用，程序应该能够正常运行！")
    else:
        print("\n⚠️  部分核心包缺失，程序可能无法正常运行")


if __name__ == "__main__":
    main()
