"""
网易云音乐登录研究配置文件
用于学术研究目的
"""

# 网易云音乐相关URL
NETEASE_URLS = {
    'login_url': 'https://music.163.com/weapi/login/cellphone',
    'captcha_url': 'https://music.163.com/weapi/login/qrcode/client/login',
    'qr_key_url': 'https://music.163.com/weapi/login/qrcode/unikey',
    'qr_create_url': 'https://music.163.com/weapi/login/qrcode/create',
    'user_info_url': 'https://music.163.com/weapi/w/nuser/account/get',
    'main_url': 'https://music.163.com',
}

# 请求头配置
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Referer': 'https://music.163.com/',
    'Origin': 'https://music.163.com',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
}

# 加密相关配置
ENCRYPT_CONFIG = {
    'modulus': '00e0b509f6259df8642dbc35662901477df22677ec152b5ff68ace615bb7b725152b3ab17a876aea8a5aa76d2e417629ec4ee341f56135fccf695280104e0312ecbda92557c93870114af6c9d05c4f7f0c3685b7a46bee255932575cce10b424d813cfe4875d3e82047b97ddef52741d546b8e289dc6935b3ece0462db0a22b8e7',
    'nonce': '0CoJUm6Qyw8W8jud',
    'pubKey': '010001'
}

# 验证码识别配置
CAPTCHA_CONFIG = {
    'ddddocr_beta': True,
    'retry_times': 3,
    'timeout': 10
}

# Selenium配置
SELENIUM_CONFIG = {
    'headless': True,
    'window_size': (1920, 1080),
    'timeout': 30,
    'chrome_options': [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--allow-running-insecure-content',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images',
        '--disable-javascript'
    ]
}

# 代理配置（可选）
PROXY_CONFIG = {
    'enabled': False,
    'http': None,
    'https': None
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'netease_research.log'
}
