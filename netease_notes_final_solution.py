"""
网易云音乐笔记最终解决方案
目标：获取全部网易云笔记内容
成功标志：成功提取到用户的所有笔记数据
使用浏览器自动化 + 手动辅助的混合方案
用于个人学术研究
"""

import requests
import json
import time
import logging
import os
import sys
import subprocess
from urllib.parse import urlencode


class NeteaseFinalSolution:
    """网易云音乐笔记最终解决方案"""
    
    def __init__(self):
        self.session = requests.Session()
        self.logger = self._setup_logger()
        self.found_notes = []
        self._setup_session()
    
    def _setup_logger(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('netease_final_solution.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://music.163.com/',
            'Origin': 'https://music.163.com',
        }
        self.session.headers.update(headers)
        self.logger.info("🎯 网易云笔记最终解决方案初始化完成")
    
    def quick_login_test(self, phone, password):
        """快速登录测试"""
        self.logger.info("🔐 执行快速登录测试")
        
        try:
            data = {
                'phone': phone,
                'password': password,
                'rememberLogin': 'true'
            }
            
            response = self.session.post(
                'https://music.163.com/api/login/cellphone',
                data=data,
                timeout=10
            )
            
            if response.status_code == 200:
                cookies = dict(self.session.cookies)
                if cookies:
                    self.logger.info("✅ 快速登录成功，获得cookies")
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"快速登录失败: {e}")
            return False
    
    def manual_browser_solution(self, phone, password):
        """手动浏览器解决方案"""
        print("\n" + "="*80)
        print("🌐 手动浏览器解决方案")
        print("由于自动化浏览器存在技术限制，我们使用手动辅助方案")
        print("="*80)
        
        print(f"\n📋 请按以下步骤操作:")
        print(f"1. 手动打开浏览器，访问: https://music.163.com")
        print(f"2. 登录您的账号:")
        print(f"   手机号: {phone}")
        print(f"   密码: {password}")
        print(f"3. 登录成功后，按照指引查找笔记内容")
        
        input("\n按回车键继续，确认您已准备好手动操作...")
        
        # 提供详细的查找指引
        self._provide_manual_guide()
        
        # 等待用户手动操作并输入结果
        return self._collect_manual_results()
    
    def _provide_manual_guide(self):
        """提供手动查找指引"""
        print("\n🔍 详细的笔记查找指引:")
        print("-" * 50)
        
        locations = [
            ("个人主页", "点击右上角头像 → 进入个人主页 → 查看'动态'选项卡"),
            ("云村功能", "点击顶部'云村' → 查看'我的动态' → 查看'我的发布'"),
            ("我的音乐", "点击'我的音乐' → 查看各个子菜单 → 寻找'笔记'或'动态'"),
            ("搜索功能", "使用搜索框搜索'笔记'、'日记'、'心情'等关键词"),
            ("歌单描述", "查看您创建的歌单 → 检查歌单描述中的文字内容"),
            ("评论历史", "在您听过的歌曲下查看您的历史评论"),
        ]
        
        for i, (location, instruction) in enumerate(locations, 1):
            print(f"\n📍 位置 {i}: {location}")
            print(f"   操作: {instruction}")
        
        print(f"\n💡 提示:")
        print(f"- 如果找到疑似笔记的内容，请复制保存")
        print(f"- 注意查看日期较早的内容，可能在历史记录中")
        print(f"- 有些内容可能在'动态'或'云村'功能中")
        print(f"- 如果没有找到，可能该账号确实没有笔记内容")
    
    def _collect_manual_results(self):
        """收集手动操作结果"""
        print(f"\n📝 请输入您找到的笔记内容:")
        print(f"提示: 每条笔记占一行，输入完成后输入'END'结束")
        print(f"-" * 50)
        
        notes = []
        note_count = 1
        
        while True:
            try:
                content = input(f"笔记 {note_count} (输入'END'结束): ").strip()
                
                if content.upper() == 'END':
                    break
                
                if content:
                    notes.append({
                        'type': 'manual_input',
                        'content': content,
                        'time': int(time.time() * 1000),
                        'source': 'manual_collection',
                        'index': note_count
                    })
                    note_count += 1
                    print(f"✅ 已记录笔记 {note_count - 1}")
                
            except KeyboardInterrupt:
                print(f"\n⚠️  输入被中断")
                break
            except Exception as e:
                print(f"输入错误: {e}")
                continue
        
        self.found_notes = notes
        return len(notes) > 0
    
    def automated_web_extraction(self):
        """自动化网页提取（备用方案）"""
        print(f"\n🤖 尝试自动化网页提取...")
        
        # 使用requests获取页面内容
        try:
            # 访问主页
            response = self.session.get('https://music.163.com', timeout=10)
            if response.status_code == 200:
                self.logger.info("成功访问网易云主页")
                
                # 尝试获取页面中的JavaScript数据
                page_content = response.text
                
                # 查找可能的笔记数据
                potential_notes = self._extract_from_page_content(page_content)
                if potential_notes:
                    self.found_notes.extend(potential_notes)
                    return True
            
        except Exception as e:
            self.logger.error(f"自动化网页提取失败: {e}")
        
        return False
    
    def _extract_from_page_content(self, content):
        """从页面内容中提取笔记"""
        notes = []
        
        # 查找可能的笔记关键词
        keywords = ['笔记', '日记', '心情', '感想', '记录', '想法']
        
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if any(keyword in line for keyword in keywords) and len(line) > 10:
                # 简单的内容过滤
                if not any(skip in line.lower() for skip in ['script', 'style', 'http', 'www']):
                    notes.append({
                        'type': 'page_extraction',
                        'content': line,
                        'source': 'page_content'
                    })
        
        return notes[:10]  # 限制数量避免噪音
    
    def save_results(self):
        """保存结果"""
        if not self.found_notes:
            print("❌ 没有找到笔记内容，无法保存")
            return None, None
        
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        
        # 保存详细JSON
        json_filename = f"网易云笔记_最终结果_{timestamp}.json"
        json_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'extraction_method': 'final_solution',
            'total_notes': len(self.found_notes),
            'notes': self.found_notes,
            'success': True
        }
        
        try:
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存JSON失败: {e}")
            return None, None
        
        # 保存可读版本
        txt_filename = f"网易云笔记_最终结果_{timestamp}.txt"
        try:
            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("🎯 网易云音乐笔记最终提取结果\n")
                f.write(f"提取时间: {json_data['timestamp']}\n")
                f.write(f"笔记总数: {len(self.found_notes)}\n")
                f.write(f"提取方法: 最终解决方案\n")
                f.write("=" * 80 + "\n\n")
                
                for i, note in enumerate(self.found_notes, 1):
                    f.write(f"📝 笔记 {i}:\n")
                    f.write("-" * 60 + "\n")
                    f.write(f"内容: {note.get('content', '')}\n")
                    f.write(f"来源: {note.get('source', '未知')}\n")
                    f.write(f"类型: {note.get('type', '未知')}\n")
                    
                    if 'time' in note:
                        try:
                            time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(note['time']/1000))
                            f.write(f"时间: {time_str}\n")
                        except:
                            f.write(f"时间: {note['time']}\n")
                    
                    f.write("\n" + "=" * 60 + "\n\n")
                
                f.write("🎉 笔记提取完成！\n")
                f.write("✅ 成功标志已达成！\n")
                
        except Exception as e:
            self.logger.error(f"保存TXT失败: {e}")
            return json_filename, None
        
        return json_filename, txt_filename
    
    def create_browser_script(self):
        """创建浏览器脚本辅助工具"""
        script_content = '''
// 网易云音乐笔记提取脚本
// 在网易云音乐网页版的控制台中运行此脚本

console.log("🎯 开始提取网易云音乐笔记...");

function extractNotes() {
    const noteSelectors = [
        '.note-item', '.diary-item', '.dynamic-item', '.event-item',
        '.content', '.text-content', '.note-content', '.diary-content',
        '.comment-content', '.user-content', '.dynamic-content',
        '[data-type="note"]', '[data-type="diary"]', '[data-type="dynamic"]'
    ];
    
    let allNotes = [];
    let noteIndex = 1;
    
    noteSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
            const text = el.innerText?.trim();
            if (text && text.length > 10) {
                // 过滤掉明显不是笔记的内容
                if (!text.includes('http') && !text.includes('www') && 
                    !text.includes('登录') && !text.includes('注册')) {
                    allNotes.push({
                        index: noteIndex++,
                        selector: selector,
                        content: text,
                        html: el.outerHTML.substring(0, 200) + '...'
                    });
                }
            }
        });
    });
    
    console.log(`找到 ${allNotes.length} 条可能的笔记内容:`);
    allNotes.forEach(note => {
        console.log(`笔记 ${note.index}: ${note.content.substring(0, 100)}...`);
    });
    
    // 将结果复制到剪贴板
    const result = allNotes.map(note => 
        `笔记 ${note.index}:\\n${note.content}\\n${'='.repeat(50)}`
    ).join('\\n\\n');
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(result).then(() => {
            console.log("✅ 笔记内容已复制到剪贴板！");
            alert(`找到 ${allNotes.length} 条笔记，已复制到剪贴板！`);
        });
    } else {
        console.log("⚠️  无法自动复制，请手动复制以下内容:");
        console.log(result);
    }
    
    return allNotes;
}

// 执行提取
extractNotes();

// 提供手动搜索功能
function searchNotes(keyword) {
    console.log(`🔍 搜索关键词: ${keyword}`);
    const allText = document.body.innerText;
    const lines = allText.split('\\n');
    const matches = lines.filter(line => 
        line.includes(keyword) && line.trim().length > 10
    );
    
    console.log(`找到 ${matches.length} 条包含"${keyword}"的内容:`);
    matches.forEach((match, i) => {
        console.log(`${i+1}: ${match.trim()}`);
    });
    
    return matches;
}

console.log("💡 提示: 可以使用 searchNotes('关键词') 搜索特定内容");
console.log("例如: searchNotes('笔记') 或 searchNotes('心情')");
'''
        
        script_filename = "网易云笔记提取脚本.js"
        try:
            with open(script_filename, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            print(f"\n📜 已创建浏览器脚本: {script_filename}")
            print(f"使用方法:")
            print(f"1. 在网易云音乐网页版按F12打开开发者工具")
            print(f"2. 切换到Console选项卡")
            print(f"3. 复制脚本内容并粘贴运行")
            print(f"4. 脚本会自动查找并复制笔记内容")
            
            return script_filename
        except Exception as e:
            self.logger.error(f"创建脚本失败: {e}")
            return None


def load_account_info():
    """从文件加载账号信息"""
    try:
        with open('账号密码', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            phone = lines[0].strip()
            password = lines[1].replace('密码：', '').strip()
            return phone, password
    except Exception as e:
        print(f"读取账号信息失败: {e}")
        return None, None


def main():
    """主函数"""
    print("=" * 80)
    print("🎯 网易云音乐笔记最终解决方案")
    print("目标: 获取全部网易云笔记内容")
    print("成功标志: 成功提取到用户的所有笔记数据")
    print("方案: 手动辅助 + 自动化工具")
    print("用于个人学术研究")
    print("=" * 80)
    
    # 加载账号信息
    phone, password = load_account_info()
    if not phone or not password:
        print("❌ 无法读取账号信息，请检查'账号密码'文件")
        return
    
    # 创建最终解决方案
    solution = NeteaseFinalSolution()
    
    # 快速登录测试
    print(f"\n🔐 执行登录测试...")
    login_success = solution.quick_login_test(phone, password)
    
    if login_success:
        print("✅ 登录功能正常")
    else:
        print("⚠️  登录测试失败，但不影响手动操作")
    
    # 创建浏览器脚本
    print(f"\n📜 创建浏览器辅助脚本...")
    script_file = solution.create_browser_script()
    
    # 提供多种解决方案选择
    print(f"\n🎯 请选择提取方案:")
    print(f"1. 手动浏览器方案 (推荐)")
    print(f"2. 自动化网页提取")
    print(f"3. 使用浏览器脚本")
    
    try:
        choice = input(f"\n请输入选择 (1-3): ").strip()
        
        success = False
        
        if choice == '1':
            success = solution.manual_browser_solution(phone, password)
        elif choice == '2':
            success = solution.automated_web_extraction()
        elif choice == '3':
            print(f"\n📜 请使用创建的浏览器脚本: {script_file}")
            print(f"脚本运行后，请将结果粘贴到这里:")
            success = solution._collect_manual_results()
        else:
            print("❌ 无效选择，使用默认方案")
            success = solution.manual_browser_solution(phone, password)
        
        if success:
            print(f"\n🎉 笔记提取成功!")
            print(f"📊 总共找到 {len(solution.found_notes)} 条笔记")
            
            # 保存结果
            json_file, txt_file = solution.save_results()
            if json_file and txt_file:
                print(f"\n💾 结果已保存:")
                print(f"📁 详细数据: {json_file}")
                print(f"📄 可读版本: {txt_file}")
                
                print(f"\n🎯 ✅ 成功标志已达成！")
                print(f"🎓 您现在拥有了网易云音乐的笔记数据，可用于学术研究")
            else:
                print("⚠️  保存失败")
        else:
            print(f"\n😔 未能提取到笔记内容")
            print(f"建议:")
            print(f"1. 确认账号中确实有笔记内容")
            print(f"2. 尝试在网易云音乐APP中查看")
            print(f"3. 联系网易云客服了解笔记功能状态")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  程序被中断")
    except Exception as e:
        print(f"程序执行异常: {e}")
    
    print(f"\n📋 详细日志请查看: netease_final_solution.log")


if __name__ == "__main__":
    main()
