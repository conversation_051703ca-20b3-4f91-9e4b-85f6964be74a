"""
网易云音乐登录绕过验证程序 - 增强版
使用浏览器自动化实现更完整的登录
用于学术研究目的
"""

import requests
import json
import time
import random
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains


class EnhancedNeteaseLogin:
    """增强版网易云登录类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.driver = None
        self.wait = None
        self.logger = self._setup_logger()
        self._setup_session()
    
    def _setup_logger(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('netease_enhanced.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(headers)
        self.logger.info("会话初始化完成")
    
    def _setup_browser(self, headless=False):
        """设置浏览器"""
        try:
            chrome_options = Options()
            
            if headless:
                chrome_options.add_argument('--headless')
            
            # 基本选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 反检测选项
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 自动下载ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.wait = WebDriverWait(self.driver, 30)
            self.logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def browser_login(self, phone, password):
        """使用浏览器自动化登录"""
        self.logger.info("开始浏览器自动化登录")
        
        if not self._setup_browser(headless=False):  # 显示浏览器便于调试
            return False, None
        
        try:
            # 访问网易云音乐
            self.logger.info("访问网易云音乐主页")
            self.driver.get('https://music.163.com')
            time.sleep(3)
            
            # 点击登录按钮
            try:
                login_btn = self.wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, '.topbar .login'))
                )
                login_btn.click()
                self.logger.info("点击登录按钮成功")
                time.sleep(2)
            except Exception as e:
                self.logger.warning(f"点击登录按钮失败: {e}")
                # 尝试直接访问登录页面
                self.driver.get('https://music.163.com/#/login')
                time.sleep(3)
            
            # 切换到手机号登录
            try:
                phone_tab = self.wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, '.tab.phone, .tab[data-tab="phone"]'))
                )
                phone_tab.click()
                self.logger.info("切换到手机号登录")
                time.sleep(1)
            except Exception as e:
                self.logger.warning(f"切换登录方式失败: {e}")
            
            # 输入手机号
            try:
                phone_input = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder*="手机"], input[name="phone"], input[type="tel"]'))
                )
                phone_input.clear()
                self._human_type(phone_input, phone)
                self.logger.info("输入手机号成功")
                time.sleep(1)
            except Exception as e:
                self.logger.error(f"输入手机号失败: {e}")
                return False, None
            
            # 输入密码
            try:
                password_input = self.driver.find_element(By.CSS_SELECTOR, 'input[type="password"], input[placeholder*="密码"]')
                password_input.clear()
                self._human_type(password_input, password)
                self.logger.info("输入密码成功")
                time.sleep(1)
            except Exception as e:
                self.logger.error(f"输入密码失败: {e}")
                return False, None
            
            # 处理验证码（如果有）
            self._handle_captcha()
            
            # 点击登录按钮
            try:
                submit_btn = self.driver.find_element(By.CSS_SELECTOR, '.login-btn, .u-btn2, button[type="submit"]')
                submit_btn.click()
                self.logger.info("点击登录按钮")
                time.sleep(3)
            except Exception as e:
                self.logger.error(f"点击登录按钮失败: {e}")
                return False, None
            
            # 检查登录结果
            success = self._check_login_success()
            
            if success:
                # 获取cookies
                cookies = self._get_browser_cookies()
                self.logger.info("浏览器登录成功")
                return True, {'cookies': cookies, 'method': 'browser'}
            else:
                self.logger.warning("浏览器登录失败")
                return False, None
                
        except Exception as e:
            self.logger.error(f"浏览器登录异常: {e}")
            return False, None
        finally:
            if self.driver:
                time.sleep(5)  # 保持浏览器开启5秒便于观察
                self.driver.quit()
    
    def _human_type(self, element, text):
        """模拟人类打字"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.2))
    
    def _handle_captcha(self):
        """处理验证码"""
        try:
            # 检查是否有图形验证码
            captcha_elements = self.driver.find_elements(By.CSS_SELECTOR, '.captcha, .verify-code, .code-input')
            if captcha_elements:
                self.logger.warning("检测到验证码，需要手动处理")
                print("\n⚠️  检测到验证码，请在浏览器中手动输入验证码")
                print("程序将等待30秒...")
                time.sleep(30)
            
            # 检查是否有滑块验证
            slide_elements = self.driver.find_elements(By.CSS_SELECTOR, '.slide-verify, .slider, .nc-container')
            if slide_elements:
                self.logger.warning("检测到滑块验证，尝试自动处理")
                self._handle_slide_captcha()
                
        except Exception as e:
            self.logger.warning(f"处理验证码时出错: {e}")
    
    def _handle_slide_captcha(self):
        """处理滑块验证码"""
        try:
            slider = self.driver.find_element(By.CSS_SELECTOR, '.slide-btn, .slider-btn, .nc-lang-cnt')
            
            # 模拟滑动
            action = ActionChains(self.driver)
            action.click_and_hold(slider)
            
            # 随机滑动距离
            for i in range(20):
                action.move_by_offset(random.randint(5, 15), random.randint(-2, 2))
                time.sleep(random.uniform(0.01, 0.05))
            
            action.release()
            action.perform()
            
            self.logger.info("滑块验证处理完成")
            time.sleep(2)
            
        except Exception as e:
            self.logger.warning(f"滑块验证处理失败: {e}")
    
    def _check_login_success(self):
        """检查登录是否成功"""
        try:
            # 等待页面变化
            time.sleep(3)
            
            # 检查URL变化
            current_url = self.driver.current_url
            if 'discover' in current_url or 'my' in current_url or '#/user' in current_url:
                self.logger.info("登录成功 - URL已跳转")
                return True
            
            # 检查用户头像或用户名
            user_elements = self.driver.find_elements(By.CSS_SELECTOR, '.user .avatar, .user .name, .topbar .login')
            for element in user_elements:
                if element.text and '登录' not in element.text:
                    self.logger.info("登录成功 - 发现用户信息")
                    return True
            
            # 检查错误信息
            error_elements = self.driver.find_elements(By.CSS_SELECTOR, '.error, .err-msg, .tip')
            for element in error_elements:
                if element.text and element.is_displayed():
                    self.logger.warning(f"登录错误: {element.text}")
                    return False
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False
    
    def _get_browser_cookies(self):
        """获取浏览器cookies"""
        try:
            cookies = self.driver.get_cookies()
            cookie_dict = {}
            for cookie in cookies:
                cookie_dict[cookie['name']] = cookie['value']
            return cookie_dict
        except Exception as e:
            self.logger.error(f"获取cookies失败: {e}")
            return {}
    
    def sync_cookies_to_session(self, cookies):
        """将cookies同步到requests会话"""
        try:
            for name, value in cookies.items():
                self.session.cookies.set(name, value, domain='.music.163.com')
            self.logger.info("Cookies同步成功")
            return True
        except Exception as e:
            self.logger.error(f"Cookies同步失败: {e}")
            return False
    
    def test_login_status(self):
        """测试登录状态"""
        try:
            test_urls = [
                'https://music.163.com/weapi/user/level',
                'https://music.163.com/api/user/account',
                'https://music.163.com/weapi/user/detail/1'
            ]
            
            for url in test_urls:
                try:
                    response = self.session.get(url, timeout=10)
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if result.get('code') == 200:
                                self.logger.info(f"登录状态测试成功: {url}")
                                return True, result
                        except:
                            pass
                except Exception as e:
                    self.logger.debug(f"测试URL {url} 失败: {e}")
                    continue
            
            return False, None
            
        except Exception as e:
            self.logger.error(f"测试登录状态失败: {e}")
            return False, None


def load_account_info():
    """从文件加载账号信息"""
    try:
        with open('账号密码', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            phone = lines[0].strip()
            password = lines[1].replace('密码：', '').strip()
            return phone, password
    except Exception as e:
        print(f"读取账号信息失败: {e}")
        return None, None


def main():
    """主函数"""
    print("=" * 60)
    print("网易云音乐登录绕过验证研究程序 - 增强版")
    print("使用浏览器自动化实现更完整的登录")
    print("仅用于学术研究目的")
    print("=" * 60)
    
    # 加载账号信息
    phone, password = load_account_info()
    if not phone or not password:
        print("无法读取账号信息，请检查'账号密码'文件")
        return
    
    # 创建登录实例
    login = EnhancedNeteaseLogin()
    
    # 尝试浏览器登录
    print(f"\n🚀 开始登录账号: {phone}")
    print("📱 将打开浏览器窗口，请观察登录过程...")
    
    success, result = login.browser_login(phone, password)
    
    if success:
        print("\n✅ 浏览器登录成功！")
        if result and 'cookies' in result:
            print(f"获得 {len(result['cookies'])} 个cookies")
            
            # 同步cookies到session
            if login.sync_cookies_to_session(result['cookies']):
                print("🔄 Cookies同步成功")
                
                # 测试登录状态
                print("🧪 测试登录状态...")
                test_success, test_result = login.test_login_status()
                if test_success:
                    print("✅ 登录状态测试成功")
                    print("测试结果:", json.dumps(test_result, indent=2, ensure_ascii=False))
                else:
                    print("⚠️  登录状态测试失败")
            else:
                print("❌ Cookies同步失败")
    else:
        print("\n❌ 浏览器登录失败！")
    
    print(f"\n📋 详细日志请查看: netease_enhanced.log")


if __name__ == "__main__":
    main()
