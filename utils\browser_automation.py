"""
浏览器自动化模块
用于处理需要浏览器环境的验证绕过
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time
import random
import logging
from fake_useragent import UserAgent


class BrowserAutomation:
    """浏览器自动化类"""
    
    def __init__(self, headless=True):
        self.logger = logging.getLogger(__name__)
        self.driver = None
        self.wait = None
        self.headless = headless
        self._setup_driver()
    
    def _setup_driver(self):
        """设置Chrome驱动"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # 基本选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 反检测选项
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 随机User-Agent
            ua = UserAgent()
            chrome_options.add_argument(f'--user-agent={ua.random}')
            
            # 自动下载ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.wait = WebDriverWait(self.driver, 30)
            self.logger.info("Chrome驱动初始化成功")
            
        except Exception as e:
            self.logger.error(f"Chrome驱动初始化失败: {e}")
            raise
    
    def navigate_to_login(self):
        """导航到登录页面"""
        try:
            self.driver.get('https://music.163.com/#/login')
            time.sleep(2)
            
            # 切换到手机号登录
            phone_tab = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '.tab.phone'))
            )
            phone_tab.click()
            time.sleep(1)
            
            self.logger.info("成功导航到登录页面")
            return True
            
        except Exception as e:
            self.logger.error(f"导航到登录页面失败: {e}")
            return False
    
    def input_credentials(self, phone, password):
        """输入登录凭据"""
        try:
            # 输入手机号
            phone_input = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder="请输入手机号"]'))
            )
            phone_input.clear()
            self._human_type(phone_input, phone)
            
            time.sleep(random.uniform(0.5, 1.5))
            
            # 输入密码
            password_input = self.driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
            password_input.clear()
            self._human_type(password_input, password)
            
            self.logger.info("成功输入登录凭据")
            return True
            
        except Exception as e:
            self.logger.error(f"输入登录凭据失败: {e}")
            return False
    
    def _human_type(self, element, text):
        """模拟人类打字"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.2))
    
    def handle_captcha(self, captcha_solver):
        """处理验证码"""
        try:
            # 检查是否有图形验证码
            captcha_img = None
            try:
                captcha_img = self.driver.find_element(By.CSS_SELECTOR, '.captcha img')
            except:
                pass
            
            if captcha_img:
                # 获取验证码图片
                captcha_src = captcha_img.get_attribute('src')
                if captcha_src:
                    # 识别验证码
                    captcha_text = captcha_solver.solve_text_captcha(captcha_src)
                    if captcha_text:
                        # 输入验证码
                        captcha_input = self.driver.find_element(By.CSS_SELECTOR, 'input[placeholder="验证码"]')
                        captcha_input.clear()
                        self._human_type(captcha_input, captcha_text)
                        self.logger.info(f"已输入验证码: {captcha_text}")
                        return True
            
            # 检查是否有滑块验证
            slide_element = None
            try:
                slide_element = self.driver.find_element(By.CSS_SELECTOR, '.slide-verify')
            except:
                pass
            
            if slide_element:
                return self._handle_slide_captcha(captcha_solver)
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理验证码失败: {e}")
            return False
    
    def _handle_slide_captcha(self, captcha_solver):
        """处理滑块验证码"""
        try:
            # 获取背景图和滑块图
            bg_img = self.driver.find_element(By.CSS_SELECTOR, '.slide-bg img')
            slide_img = self.driver.find_element(By.CSS_SELECTOR, '.slide-block img')
            
            bg_src = bg_img.get_attribute('src')
            slide_src = slide_img.get_attribute('src')
            
            # 计算滑动距离
            distance = captcha_solver.solve_slide_captcha(bg_src, slide_src)
            
            if distance > 0:
                # 执行滑动
                slider = self.driver.find_element(By.CSS_SELECTOR, '.slide-btn')
                self._simulate_slide(slider, distance)
                self.logger.info(f"已执行滑块验证，距离: {distance}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理滑块验证码失败: {e}")
            return False
    
    def _simulate_slide(self, element, distance):
        """模拟滑动操作"""
        try:
            action = ActionChains(self.driver)
            action.click_and_hold(element)
            
            # 分段滑动，模拟人类行为
            current_distance = 0
            while current_distance < distance:
                move_distance = min(random.randint(5, 15), distance - current_distance)
                action.move_by_offset(move_distance, random.randint(-2, 2))
                current_distance += move_distance
                time.sleep(random.uniform(0.01, 0.05))
            
            # 随机小幅回调
            action.move_by_offset(random.randint(-5, 0), 0)
            time.sleep(random.uniform(0.1, 0.3))
            action.release()
            action.perform()
            
        except Exception as e:
            self.logger.error(f"模拟滑动失败: {e}")
    
    def click_login(self):
        """点击登录按钮"""
        try:
            login_btn = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '.login-btn'))
            )
            login_btn.click()
            self.logger.info("已点击登录按钮")
            return True
            
        except Exception as e:
            self.logger.error(f"点击登录按钮失败: {e}")
            return False
    
    def check_login_success(self):
        """检查登录是否成功"""
        try:
            # 等待页面跳转或用户信息出现
            time.sleep(3)
            
            # 检查是否跳转到主页
            current_url = self.driver.current_url
            if 'discover' in current_url or 'my' in current_url:
                self.logger.info("登录成功 - 页面已跳转")
                return True
            
            # 检查是否有用户头像
            try:
                user_avatar = self.driver.find_element(By.CSS_SELECTOR, '.user .avatar')
                if user_avatar:
                    self.logger.info("登录成功 - 发现用户头像")
                    return True
            except:
                pass
            
            # 检查是否有错误信息
            try:
                error_msg = self.driver.find_element(By.CSS_SELECTOR, '.error-msg')
                if error_msg and error_msg.text:
                    self.logger.error(f"登录失败: {error_msg.text}")
                    return False
            except:
                pass
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False
    
    def get_cookies(self):
        """获取登录后的cookies"""
        try:
            cookies = self.driver.get_cookies()
            cookie_dict = {}
            for cookie in cookies:
                cookie_dict[cookie['name']] = cookie['value']
            return cookie_dict
        except Exception as e:
            self.logger.error(f"获取cookies失败: {e}")
            return {}
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            self.logger.info("浏览器已关闭")
