"""
验证码识别模块
用于自动识别和处理各种验证码
"""

import ddddocr
import cv2
import numpy as np
from PIL import Image
import io
import base64
import requests
import time
import logging


class CaptchaSolver:
    """验证码解决器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        try:
            self.ocr = ddddocr.DdddOcr(beta=True)
            self.slide_ocr = ddddocr.DdddOcr(det=False, ocr=False)
            self.logger.info("验证码识别器初始化成功")
        except Exception as e:
            self.logger.error(f"验证码识别器初始化失败: {e}")
            self.ocr = None
            self.slide_ocr = None
    
    def solve_text_captcha(self, image_data):
        """识别文字验证码"""
        try:
            if isinstance(image_data, str):
                # base64格式
                if image_data.startswith('data:image'):
                    image_data = image_data.split(',')[1]
                image_bytes = base64.b64decode(image_data)
            else:
                image_bytes = image_data
            
            if self.ocr:
                result = self.ocr.classification(image_bytes)
                self.logger.info(f"文字验证码识别结果: {result}")
                return result
            else:
                self.logger.error("OCR引擎未初始化")
                return None
                
        except Exception as e:
            self.logger.error(f"文字验证码识别失败: {e}")
            return None
    
    def solve_slide_captcha(self, background_img, slide_img):
        """解决滑块验证码"""
        try:
            if isinstance(background_img, str):
                if background_img.startswith('data:image'):
                    background_img = background_img.split(',')[1]
                bg_bytes = base64.b64decode(background_img)
            else:
                bg_bytes = background_img
                
            if isinstance(slide_img, str):
                if slide_img.startswith('data:image'):
                    slide_img = slide_img.split(',')[1]
                slide_bytes = base64.b64decode(slide_img)
            else:
                slide_bytes = slide_img
            
            if self.slide_ocr:
                result = self.slide_ocr.slide_match(slide_bytes, bg_bytes, simple_target=True)
                distance = result['target'][0] if result else 0
                self.logger.info(f"滑块验证码识别距离: {distance}")
                return distance
            else:
                # 备用方案：使用OpenCV模板匹配
                return self._opencv_slide_match(bg_bytes, slide_bytes)
                
        except Exception as e:
            self.logger.error(f"滑块验证码识别失败: {e}")
            return 0
    
    def _opencv_slide_match(self, bg_bytes, slide_bytes):
        """使用OpenCV进行滑块匹配"""
        try:
            # 转换为OpenCV图像
            bg_img = cv2.imdecode(np.frombuffer(bg_bytes, np.uint8), cv2.IMREAD_COLOR)
            slide_img = cv2.imdecode(np.frombuffer(slide_bytes, np.uint8), cv2.IMREAD_COLOR)
            
            # 转换为灰度图
            bg_gray = cv2.cvtColor(bg_img, cv2.COLOR_BGR2GRAY)
            slide_gray = cv2.cvtColor(slide_img, cv2.COLOR_BGR2GRAY)
            
            # 模板匹配
            result = cv2.matchTemplate(bg_gray, slide_gray, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            return max_loc[0]
            
        except Exception as e:
            self.logger.error(f"OpenCV滑块匹配失败: {e}")
            return 0
    
    def preprocess_captcha_image(self, image_bytes):
        """预处理验证码图像"""
        try:
            # 转换为PIL图像
            image = Image.open(io.BytesIO(image_bytes))
            
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 调整大小（如果需要）
            if image.size[0] > 300 or image.size[1] > 100:
                image = image.resize((200, 80), Image.Resampling.LANCZOS)
            
            # 增强对比度
            image = self._enhance_contrast(image)
            
            # 转换回字节
            output = io.BytesIO()
            image.save(output, format='PNG')
            return output.getvalue()
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {e}")
            return image_bytes
    
    def _enhance_contrast(self, image):
        """增强图像对比度"""
        try:
            # 转换为numpy数组
            img_array = np.array(image)
            
            # 应用直方图均衡化
            img_yuv = cv2.cvtColor(img_array, cv2.COLOR_RGB2YUV)
            img_yuv[:,:,0] = cv2.equalizeHist(img_yuv[:,:,0])
            img_enhanced = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2RGB)
            
            return Image.fromarray(img_enhanced)
            
        except Exception as e:
            self.logger.error(f"对比度增强失败: {e}")
            return image
    
    def simulate_human_slide(self, distance):
        """模拟人类滑动轨迹"""
        tracks = []
        current = 0
        mid = distance * 4 / 5
        t = 0.2
        v = 0
        
        while current < distance:
            if current < mid:
                a = 2
            else:
                a = -3
            
            v0 = v
            v = v0 + a * t
            move = v0 * t + 1 / 2 * a * t * t
            current += move
            tracks.append(round(move))
        
        # 添加一些随机抖动
        for i in range(len(tracks)):
            tracks[i] += np.random.randint(-1, 2)
        
        return tracks
