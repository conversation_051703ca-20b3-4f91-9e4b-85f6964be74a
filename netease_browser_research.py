"""
网易云音乐登录绕过验证并浏览器查看内容程序
用于个人学术研究
"""

import requests
import json
import time
import logging
import hashlib
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
import os


class NeteaseResearchBrowser:
    """网易云音乐研究浏览器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.driver = None
        self.wait = None
        self.logger = self._setup_logger()
        self._setup_session()
    
    def _setup_logger(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('netease_research.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://music.163.com/',
            'Origin': 'https://music.163.com',
        }
        self.session.headers.update(headers)
        self.logger.info("研究会话初始化完成")
    
    def setup_browser(self, headless=False):
        """设置浏览器"""
        try:
            chrome_options = Options()
            
            if headless:
                chrome_options.add_argument('--headless')
            
            # 基本选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            
            # 反检测选项
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 尝试使用系统Chrome
            try:
                self.driver = webdriver.Chrome(options=chrome_options)
                self.logger.info("使用系统Chrome浏览器")
            except Exception as e:
                self.logger.warning(f"系统Chrome启动失败: {e}")
                # 尝试指定Chrome路径
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
                ]
                
                for path in chrome_paths:
                    if os.path.exists(path):
                        chrome_options.binary_location = path
                        try:
                            self.driver = webdriver.Chrome(options=chrome_options)
                            self.logger.info(f"使用Chrome路径: {path}")
                            break
                        except:
                            continue
                else:
                    raise Exception("无法找到Chrome浏览器")
            
            # 反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en']
                    });
                '''
            })
            
            self.wait = WebDriverWait(self.driver, 30)
            self.logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def api_login_bypass(self, phone, password):
        """API登录绕过验证"""
        self.logger.info("尝试API登录绕过验证")
        
        try:
            # 方法1: 简化表单登录
            login_data = {
                'phone': phone,
                'password': password,
                'rememberLogin': 'true'
            }
            
            response = self.session.post(
                'https://music.163.com/api/login/cellphone',
                data=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.logger.info("API登录可能成功")
                return True, dict(self.session.cookies)
            
            # 方法2: 加密登录
            encrypted_password = hashlib.md5(password.encode('utf-8')).hexdigest()
            login_data['password'] = encrypted_password
            
            response = self.session.post(
                'https://music.163.com/api/login/cellphone',
                data=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.logger.info("加密API登录可能成功")
                return True, dict(self.session.cookies)
                
        except Exception as e:
            self.logger.error(f"API登录失败: {e}")
        
        return False, None
    
    def browser_login_bypass(self, phone, password):
        """浏览器登录绕过验证"""
        self.logger.info("开始浏览器登录绕过验证")
        
        if not self.setup_browser(headless=False):
            return False
        
        try:
            # 访问网易云音乐
            self.logger.info("访问网易云音乐")
            self.driver.get('https://music.163.com')
            time.sleep(3)
            
            # 点击登录
            try:
                login_btn = self.wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, '.topbar .login, .u-btn-login'))
                )
                login_btn.click()
                self.logger.info("点击登录按钮")
                time.sleep(2)
            except:
                # 直接访问登录页面
                self.driver.get('https://music.163.com/#/login')
                time.sleep(3)
            
            # 切换到手机号登录
            try:
                phone_tab = self.driver.find_element(By.CSS_SELECTOR, '.tab.phone, [data-tab="phone"]')
                phone_tab.click()
                time.sleep(1)
            except:
                pass
            
            # 输入手机号
            phone_input = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder*="手机"], input[name="phone"]'))
            )
            phone_input.clear()
            self._human_type(phone_input, phone)
            time.sleep(1)
            
            # 输入密码
            password_input = self.driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
            password_input.clear()
            self._human_type(password_input, password)
            time.sleep(1)
            
            # 处理验证码
            self._handle_verification()
            
            # 点击登录
            login_submit = self.driver.find_element(By.CSS_SELECTOR, '.login-btn, .u-btn2')
            login_submit.click()
            self.logger.info("提交登录")
            time.sleep(5)
            
            # 检查登录结果
            if self._check_login_success():
                cookies = self._get_browser_cookies()
                self._sync_cookies_to_session(cookies)
                self.logger.info("浏览器登录成功")
                return True
            else:
                self.logger.warning("浏览器登录失败")
                return False
                
        except Exception as e:
            self.logger.error(f"浏览器登录异常: {e}")
            return False
    
    def _human_type(self, element, text):
        """模拟人类打字"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.2))
    
    def _handle_verification(self):
        """处理验证码"""
        try:
            # 检查图形验证码
            captcha_imgs = self.driver.find_elements(By.CSS_SELECTOR, '.captcha img, .verify-code img')
            if captcha_imgs:
                print("\n⚠️  检测到图形验证码，请手动输入")
                time.sleep(10)
            
            # 检查滑块验证
            sliders = self.driver.find_elements(By.CSS_SELECTOR, '.slide-verify, .nc-container')
            if sliders:
                print("\n🔄 检测到滑块验证，尝试自动处理")
                self._handle_slider()
                
        except Exception as e:
            self.logger.warning(f"验证码处理异常: {e}")
    
    def _handle_slider(self):
        """处理滑块验证"""
        try:
            slider = self.driver.find_element(By.CSS_SELECTOR, '.slide-btn, .nc-lang-cnt')
            action = ActionChains(self.driver)
            action.click_and_hold(slider)
            
            # 模拟人类滑动
            for i in range(30):
                action.move_by_offset(random.randint(3, 8), random.randint(-1, 1))
                time.sleep(random.uniform(0.01, 0.03))
            
            action.release().perform()
            time.sleep(2)
            
        except Exception as e:
            self.logger.warning(f"滑块处理失败: {e}")
    
    def _check_login_success(self):
        """检查登录是否成功"""
        try:
            time.sleep(3)
            current_url = self.driver.current_url
            
            # 检查URL变化
            if any(keyword in current_url for keyword in ['discover', 'my', 'user']):
                return True
            
            # 检查用户元素
            user_elements = self.driver.find_elements(By.CSS_SELECTOR, '.user .avatar, .topbar .user')
            if user_elements:
                return True
            
            # 检查错误信息
            errors = self.driver.find_elements(By.CSS_SELECTOR, '.error, .err-msg')
            if errors and any(error.is_displayed() for error in errors):
                return False
            
            return False
            
        except:
            return False
    
    def _get_browser_cookies(self):
        """获取浏览器cookies"""
        try:
            cookies = self.driver.get_cookies()
            cookie_dict = {}
            for cookie in cookies:
                cookie_dict[cookie['name']] = cookie['value']
            return cookie_dict
        except:
            return {}
    
    def _sync_cookies_to_session(self, cookies):
        """同步cookies到session"""
        try:
            for name, value in cookies.items():
                self.session.cookies.set(name, value, domain='.music.163.com')
            self.logger.info("Cookies同步成功")
        except Exception as e:
            self.logger.error(f"Cookies同步失败: {e}")
    
    def browse_content(self, keep_browser_open=True):
        """浏览内容"""
        if not self.driver:
            self.logger.error("浏览器未初始化")
            return
        
        print("\n🌐 浏览器已准备就绪，您可以:")
        print("1. 手动浏览任何页面")
        print("2. 查看个人信息、笔记、动态等")
        print("3. 程序会保持浏览器开启状态")
        
        # 访问一些常用页面
        useful_urls = [
            ('个人主页', 'https://music.163.com/#/user/home'),
            ('我的音乐', 'https://music.163.com/#/my'),
            ('云村', 'https://music.163.com/#/discover/yunmusic'),
            ('发现音乐', 'https://music.163.com/#/discover')
        ]
        
        print(f"\n📋 推荐访问的页面:")
        for name, url in useful_urls:
            print(f"- {name}: {url}")
        
        if keep_browser_open:
            print(f"\n⏳ 浏览器将保持开启状态...")
            print("按 Ctrl+C 或关闭程序来结束")
            
            try:
                while True:
                    time.sleep(10)
                    # 检查浏览器是否还在运行
                    try:
                        self.driver.current_url
                    except:
                        print("浏览器已关闭")
                        break
            except KeyboardInterrupt:
                print("\n程序结束")
        
        return True
    
    def extract_page_content(self, save_to_file=True):
        """提取当前页面内容"""
        if not self.driver:
            return None
        
        try:
            # 获取页面基本信息
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            # 提取文本内容
            page_text = self.driver.execute_script("return document.body.innerText;")
            
            # 提取特定元素
            content_selectors = [
                '.content', '.text', '.note', '.dynamic',
                '.comment', '.desc', '.intro', '.detail'
            ]
            
            extracted_content = {
                'url': current_url,
                'title': page_title,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'full_text': page_text,
                'specific_content': {}
            }
            
            for selector in content_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        extracted_content['specific_content'][selector] = [
                            el.text.strip() for el in elements if el.text.strip()
                        ]
                except:
                    continue
            
            if save_to_file:
                filename = f"页面内容_{time.strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(extracted_content, f, ensure_ascii=False, indent=2)
                print(f"📁 页面内容已保存到: {filename}")
            
            return extracted_content
            
        except Exception as e:
            self.logger.error(f"内容提取失败: {e}")
            return None
    
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            self.logger.info("浏览器已关闭")


def load_account_info():
    """从文件加载账号信息"""
    try:
        with open('账号密码', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            phone = lines[0].strip()
            password = lines[1].replace('密码：', '').strip()
            return phone, password
    except Exception as e:
        print(f"读取账号信息失败: {e}")
        return None, None


def main():
    """主函数"""
    print("=" * 70)
    print("网易云音乐登录绕过验证并浏览器查看内容程序")
    print("用于个人学术研究")
    print("=" * 70)
    
    # 加载账号信息
    phone, password = load_account_info()
    if not phone or not password:
        print("无法读取账号信息，请检查'账号密码'文件")
        return
    
    # 创建研究浏览器
    research_browser = NeteaseResearchBrowser()
    
    print(f"\n🔐 开始登录账号: {phone}")
    
    # 尝试API登录
    print("\n📡 尝试API登录绕过...")
    api_success, cookies = research_browser.api_login_bypass(phone, password)
    
    if api_success:
        print("✅ API登录成功")
    else:
        print("⚠️  API登录失败，尝试浏览器登录")
    
    # 浏览器登录
    print("\n🌐 启动浏览器登录...")
    browser_success = research_browser.browser_login_bypass(phone, password)
    
    if browser_success:
        print("✅ 浏览器登录成功")
        
        # 开始浏览内容
        print("\n🔍 开始内容浏览模式...")
        research_browser.browse_content(keep_browser_open=True)
        
    else:
        print("❌ 登录失败")
        print("请检查账号密码或网络连接")
    
    # 清理
    research_browser.close_browser()
    print(f"\n📋 详细日志请查看: netease_research.log")


if __name__ == "__main__":
    main()
