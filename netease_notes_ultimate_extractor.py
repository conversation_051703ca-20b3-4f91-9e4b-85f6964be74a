"""
网易云音乐笔记终极提取器
目标：获取全部网易云笔记内容
成功标志：成功提取到用户的所有笔记数据
用于个人学术研究
"""

import requests
import json
import time
import logging
import hashlib
import random
import string
import os
import sys
from urllib.parse import urlencode, quote
import re


class NeteaseNotesUltimateExtractor:
    """网易云音乐笔记终极提取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.logger = self._setup_logger()
        self.user_id = None
        self.user_info = None
        self.found_notes = []
        self.success = False
        self._setup_session()
    
    def _setup_logger(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('netease_notes_ultimate.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://music.163.com/',
            'Origin': 'https://music.163.com',
            'X-Requested-With': 'XMLHttpRequest'
        }
        self.session.headers.update(headers)
        self.logger.info("🚀 网易云笔记终极提取器初始化完成")
    
    def ultimate_login(self, phone, password):
        """终极登录方法 - 尝试所有可能的登录方式"""
        self.logger.info(f"🔐 开始终极登录: {phone}")
        
        login_methods = [
            self._method_simple_form,
            self._method_encrypted_form,
            self._method_weapi_login,
            self._method_api_login,
            self._method_mobile_login
        ]
        
        for i, method in enumerate(login_methods, 1):
            try:
                self.logger.info(f"🔄 尝试登录方法 {i}/5")
                if method(phone, password):
                    self.logger.info(f"✅ 登录方法 {i} 成功!")
                    return True
                time.sleep(2)  # 避免请求过快
            except Exception as e:
                self.logger.warning(f"登录方法 {i} 异常: {e}")
                continue
        
        self.logger.error("❌ 所有登录方法都失败了")
        return False
    
    def _method_simple_form(self, phone, password):
        """方法1: 简单表单登录"""
        try:
            data = {
                'phone': phone,
                'password': password,
                'rememberLogin': 'true'
            }
            
            response = self.session.post(
                'https://music.163.com/api/login/cellphone',
                data=data,
                timeout=10
            )
            
            return self._check_login_response(response)
        except:
            return False
    
    def _method_encrypted_form(self, phone, password):
        """方法2: 加密表单登录"""
        try:
            encrypted_password = hashlib.md5(password.encode('utf-8')).hexdigest()
            data = {
                'phone': phone,
                'password': encrypted_password,
                'rememberLogin': 'true'
            }
            
            response = self.session.post(
                'https://music.163.com/api/login/cellphone',
                data=data,
                timeout=10
            )
            
            return self._check_login_response(response)
        except:
            return False
    
    def _method_weapi_login(self, phone, password):
        """方法3: WebAPI登录"""
        try:
            data = {
                'phone': phone,
                'password': hashlib.md5(password.encode('utf-8')).hexdigest(),
                'rememberLogin': 'true'
            }
            
            response = self.session.post(
                'https://music.163.com/weapi/login/cellphone',
                data=data,
                timeout=10
            )
            
            return self._check_login_response(response)
        except:
            return False
    
    def _method_api_login(self, phone, password):
        """方法4: 直接API登录"""
        try:
            # 先访问主页建立会话
            self.session.get('https://music.163.com')
            
            data = {
                'phone': phone,
                'password': password,
                'rememberLogin': 'true',
                'csrf_token': self._generate_csrf_token()
            }
            
            response = self.session.post(
                'https://music.163.com/api/login/cellphone',
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            return self._check_login_response(response)
        except:
            return False
    
    def _method_mobile_login(self, phone, password):
        """方法5: 移动端登录"""
        try:
            mobile_headers = {
                'User-Agent': 'NeteaseMusic/8.7.01.220325 (iPhone; iOS 15.4; Scale/3.00)',
                'Accept': 'application/json',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {
                'phone': phone,
                'password': hashlib.md5(password.encode('utf-8')).hexdigest(),
                'rememberLogin': 'true'
            }
            
            response = self.session.post(
                'https://music.163.com/api/login/cellphone',
                data=data,
                headers=mobile_headers,
                timeout=10
            )
            
            return self._check_login_response(response)
        except:
            return False
    
    def _check_login_response(self, response):
        """检查登录响应"""
        try:
            if response.status_code == 200:
                # 检查cookies
                cookies = dict(self.session.cookies)
                if cookies:
                    self.logger.debug(f"获得cookies: {list(cookies.keys())}")
                    # 尝试获取用户信息验证登录
                    if self._verify_login():
                        return True
                
                # 即使没有用户信息，如果有cookies也认为可能成功
                if len(cookies) > 1:
                    return True
            
            return False
        except:
            return False
    
    def _verify_login(self):
        """验证登录状态"""
        try:
            test_urls = [
                'https://music.163.com/api/nuser/account/get',
                'https://music.163.com/weapi/w/nuser/account/get',
                'https://music.163.com/api/user/subcount'
            ]
            
            for url in test_urls:
                try:
                    response = self.session.get(url, timeout=5)
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if 'profile' in result:
                                self.user_info = result
                                self.user_id = result['profile'].get('userId')
                                self.logger.info(f"✅ 验证登录成功: {result['profile'].get('nickname', '未知用户')}")
                                return True
                        except:
                            pass
                except:
                    continue
            
            return False
        except:
            return False
    
    def _generate_csrf_token(self):
        """生成CSRF令牌"""
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(32))
    
    def extract_all_notes(self):
        """提取所有笔记 - 核心功能"""
        self.logger.info("🎯 开始提取所有笔记内容...")
        
        extraction_methods = [
            self._extract_via_api,
            self._extract_via_search,
            self._extract_via_user_events,
            self._extract_via_comments,
            self._extract_via_dynamic
        ]
        
        total_notes = 0
        
        for i, method in enumerate(extraction_methods, 1):
            try:
                self.logger.info(f"📝 执行提取方法 {i}/5")
                notes = method()
                if notes:
                    self.found_notes.extend(notes)
                    total_notes += len(notes)
                    self.logger.info(f"✅ 方法 {i} 找到 {len(notes)} 条笔记")
                else:
                    self.logger.info(f"⚠️  方法 {i} 未找到笔记")
            except Exception as e:
                self.logger.warning(f"方法 {i} 异常: {e}")
                continue
        
        # 去重
        self.found_notes = self._deduplicate_notes(self.found_notes)
        final_count = len(self.found_notes)
        
        self.logger.info(f"🎉 总共找到 {final_count} 条独特的笔记内容")
        
        if final_count > 0:
            self.success = True
            return True
        else:
            return False
    
    def _extract_via_api(self):
        """方法1: 通过API提取笔记"""
        notes = []
        
        if not self.user_id:
            return notes
        
        api_endpoints = [
            f'https://music.163.com/api/user/notes/{self.user_id}',
            f'https://music.163.com/weapi/user/notes/{self.user_id}',
            f'https://music.163.com/api/user/{self.user_id}/notes',
            f'https://music.163.com/weapi/user/{self.user_id}/notes',
            'https://music.163.com/api/user/notes',
            'https://music.163.com/weapi/user/notes',
            'https://music.163.com/api/notes/list',
            'https://music.163.com/weapi/notes/list'
        ]
        
        for endpoint in api_endpoints:
            try:
                response = self.session.get(endpoint, timeout=10)
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if 'notes' in result:
                            notes.extend(result['notes'])
                        elif 'data' in result and isinstance(result['data'], list):
                            notes.extend(result['data'])
                        elif isinstance(result, list):
                            notes.extend(result)
                    except:
                        pass
            except:
                continue
        
        return notes
    
    def _extract_via_search(self):
        """方法2: 通过搜索提取笔记"""
        notes = []
        
        # 搜索用户名相关的内容
        if self.user_info and 'profile' in self.user_info:
            nickname = self.user_info['profile'].get('nickname', '')
            if nickname:
                search_results = self._search_content(nickname, [1002, 1004, 1006, 1009])
                notes.extend(self._extract_notes_from_search(search_results))
        
        # 搜索常见笔记关键词
        keywords = ['笔记', '日记', '心情', '感想', '记录', '想法', '随笔']
        for keyword in keywords:
            search_results = self._search_content(keyword, [1002, 1004, 1006, 1009])
            notes.extend(self._extract_notes_from_search(search_results))
        
        return notes
    
    def _extract_via_user_events(self):
        """方法3: 通过用户动态提取笔记"""
        notes = []
        
        if not self.user_id:
            return notes
        
        event_apis = [
            f'https://music.163.com/api/user/event?uid={self.user_id}&limit=100',
            f'https://music.163.com/weapi/user/event?uid={self.user_id}&limit=100',
            'https://music.163.com/api/event/get?limit=100',
            'https://music.163.com/weapi/event/get?limit=100'
        ]
        
        for api in event_apis:
            try:
                response = self.session.get(api, timeout=10)
                if response.status_code == 200:
                    try:
                        result = response.json()
                        events = result.get('event', result.get('events', []))
                        for event in events:
                            if self._is_note_event(event):
                                note_content = self._extract_note_from_event(event)
                                if note_content:
                                    notes.append(note_content)
                    except:
                        pass
            except:
                continue
        
        return notes
    
    def _extract_via_comments(self):
        """方法4: 通过评论提取笔记"""
        notes = []
        
        if not self.user_id:
            return notes
        
        # 获取用户的评论历史
        comment_apis = [
            f'https://music.163.com/api/user/comments/{self.user_id}',
            f'https://music.163.com/weapi/user/comments/{self.user_id}'
        ]
        
        for api in comment_apis:
            try:
                response = self.session.get(api, timeout=10)
                if response.status_code == 200:
                    try:
                        result = response.json()
                        comments = result.get('comments', [])
                        for comment in comments:
                            if self._is_note_comment(comment):
                                notes.append({
                                    'type': 'comment_note',
                                    'content': comment.get('content', ''),
                                    'time': comment.get('time', 0),
                                    'source': 'comment'
                                })
                    except:
                        pass
            except:
                continue
        
        return notes
    
    def _extract_via_dynamic(self):
        """方法5: 通过动态内容提取笔记"""
        notes = []
        
        dynamic_apis = [
            'https://music.163.com/api/yunmusic/dynamic/get',
            'https://music.163.com/weapi/yunmusic/dynamic/get',
            'https://music.163.com/api/dynamic/get',
            'https://music.163.com/weapi/dynamic/get'
        ]
        
        for api in dynamic_apis:
            try:
                response = self.session.get(api, timeout=10)
                if response.status_code == 200:
                    try:
                        result = response.json()
                        dynamics = result.get('data', result.get('dynamics', []))
                        for dynamic in dynamics:
                            if self._is_note_dynamic(dynamic):
                                note_content = self._extract_note_from_dynamic(dynamic)
                                if note_content:
                                    notes.append(note_content)
                    except:
                        pass
            except:
                continue
        
        return notes
    
    def _search_content(self, keyword, types):
        """搜索内容"""
        results = []
        
        for search_type in types:
            try:
                params = {
                    's': keyword,
                    'type': search_type,
                    'limit': 100,
                    'offset': 0
                }
                
                url = f"https://music.163.com/api/search/get?{urlencode(params)}"
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if 'result' in result:
                            results.append(result['result'])
                    except:
                        pass
            except:
                continue
        
        return results
    
    def _extract_notes_from_search(self, search_results):
        """从搜索结果中提取笔记"""
        notes = []
        
        for result in search_results:
            if isinstance(result, dict):
                # 检查各种可能的笔记字段
                for key in ['notes', 'diaries', 'contents', 'texts']:
                    if key in result and isinstance(result[key], list):
                        for item in result[key]:
                            if isinstance(item, dict) and 'content' in item:
                                notes.append({
                                    'type': 'search_note',
                                    'content': item['content'],
                                    'source': 'search',
                                    'search_type': key
                                })
        
        return notes
    
    def _is_note_event(self, event):
        """判断是否为笔记事件"""
        if not isinstance(event, dict):
            return False
        
        # 检查事件类型和内容
        event_type = event.get('type', 0)
        content = event.get('info', {}).get('content', '')
        
        # 笔记相关的事件类型或包含笔记关键词
        note_indicators = ['笔记', '日记', '心情', '感想', '记录']
        return any(indicator in content for indicator in note_indicators)
    
    def _extract_note_from_event(self, event):
        """从事件中提取笔记内容"""
        try:
            info = event.get('info', {})
            return {
                'type': 'event_note',
                'content': info.get('content', ''),
                'time': event.get('eventTime', 0),
                'source': 'event'
            }
        except:
            return None
    
    def _is_note_comment(self, comment):
        """判断评论是否为笔记"""
        content = comment.get('content', '')
        return len(content) > 20 and any(keyword in content for keyword in ['笔记', '日记', '心情', '感想'])
    
    def _is_note_dynamic(self, dynamic):
        """判断动态是否为笔记"""
        content = dynamic.get('content', dynamic.get('text', ''))
        return len(content) > 10
    
    def _extract_note_from_dynamic(self, dynamic):
        """从动态中提取笔记"""
        try:
            return {
                'type': 'dynamic_note',
                'content': dynamic.get('content', dynamic.get('text', '')),
                'time': dynamic.get('time', 0),
                'source': 'dynamic'
            }
        except:
            return None
    
    def _deduplicate_notes(self, notes):
        """去重笔记"""
        seen = set()
        unique_notes = []
        
        for note in notes:
            content = note.get('content', '')
            if content and content not in seen:
                seen.add(content)
                unique_notes.append(note)
        
        return unique_notes
    
    def save_notes(self):
        """保存笔记到文件"""
        if not self.found_notes:
            self.logger.warning("没有找到笔记内容，无法保存")
            return None, None
        
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        
        # 保存JSON格式
        json_filename = f"网易云笔记_完整提取_{timestamp}.json"
        json_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'user_info': self.user_info,
            'total_notes': len(self.found_notes),
            'notes': self.found_notes,
            'extraction_success': self.success
        }
        
        try:
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存JSON文件失败: {e}")
            return None, None
        
        # 保存可读格式
        txt_filename = f"网易云笔记_可读版本_{timestamp}.txt"
        try:
            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("网易云音乐笔记完整提取结果\n")
                f.write(f"提取时间: {json_data['timestamp']}\n")
                f.write(f"笔记总数: {len(self.found_notes)}\n")
                f.write("=" * 80 + "\n\n")
                
                if self.user_info and 'profile' in self.user_info:
                    profile = self.user_info['profile']
                    f.write(f"用户信息:\n")
                    f.write(f"  昵称: {profile.get('nickname', '未知')}\n")
                    f.write(f"  用户ID: {profile.get('userId', '未知')}\n")
                    f.write(f"  签名: {profile.get('signature', '无')}\n\n")
                
                for i, note in enumerate(self.found_notes, 1):
                    f.write(f"📝 笔记 {i}:\n")
                    f.write("-" * 50 + "\n")
                    f.write(f"内容: {note.get('content', '')}\n")
                    f.write(f"类型: {note.get('type', '未知')}\n")
                    f.write(f"来源: {note.get('source', '未知')}\n")
                    
                    if 'time' in note and note['time']:
                        try:
                            time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(note['time']/1000))
                            f.write(f"时间: {time_str}\n")
                        except:
                            f.write(f"时间: {note['time']}\n")
                    
                    f.write("\n" + "=" * 50 + "\n\n")
                
        except Exception as e:
            self.logger.error(f"保存TXT文件失败: {e}")
            return json_filename, None
        
        return json_filename, txt_filename


def load_account_info():
    """从文件加载账号信息"""
    try:
        with open('账号密码', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            phone = lines[0].strip()
            password = lines[1].replace('密码：', '').strip()
            return phone, password
    except Exception as e:
        print(f"读取账号信息失败: {e}")
        return None, None


def main():
    """主函数"""
    print("=" * 80)
    print("🎯 网易云音乐笔记终极提取器")
    print("目标: 获取全部网易云笔记内容")
    print("成功标志: 成功提取到用户的所有笔记数据")
    print("用于个人学术研究")
    print("=" * 80)
    
    # 加载账号信息
    phone, password = load_account_info()
    if not phone or not password:
        print("❌ 无法读取账号信息，请检查'账号密码'文件")
        return
    
    # 创建终极提取器
    extractor = NeteaseNotesUltimateExtractor()
    
    # 执行终极登录
    print(f"\n🔐 开始终极登录...")
    if not extractor.ultimate_login(phone, password):
        print("❌ 登录失败，无法继续提取笔记")
        return
    
    print("✅ 登录成功！")
    
    # 提取所有笔记
    print(f"\n🎯 开始提取所有笔记内容...")
    success = extractor.extract_all_notes()
    
    if success:
        print(f"\n🎉 笔记提取成功！")
        print(f"📊 总共找到 {len(extractor.found_notes)} 条笔记")
        
        # 保存笔记
        json_file, txt_file = extractor.save_notes()
        if json_file and txt_file:
            print(f"\n💾 笔记已保存:")
            print(f"📁 详细数据: {json_file}")
            print(f"📄 可读版本: {txt_file}")
            
            print(f"\n✅ 任务完成！成功标志已达成！")
            print(f"🎓 您现在拥有了完整的网易云笔记数据，可用于学术研究")
        else:
            print("⚠️  笔记保存失败")
    else:
        print(f"\n😔 未能找到笔记内容")
        print("可能的原因:")
        print("1. 账号中确实没有笔记")
        print("2. 笔记功能已迁移到其他位置")
        print("3. 需要更高级的权限")
        print("4. API接口已发生变化")
    
    print(f"\n📋 详细日志请查看: netease_notes_ultimate.log")


if __name__ == "__main__":
    main()
