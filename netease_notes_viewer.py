"""
网易云音乐笔记查看器
用于获取和查看网易云音乐中的笔记内容
用于学术研究目的
"""

import requests
import json
import time
import logging
import hashlib
from netease_login_simple import SimpleNeteaseLogin


class NeteaseNotesViewer:
    """网易云音乐笔记查看器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.logger = self._setup_logger()
        self.login_instance = None
        self._setup_session()
    
    def _setup_logger(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('netease_notes.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://music.163.com/',
            'Origin': 'https://music.163.com',
        }
        self.session.headers.update(headers)
        self.logger.info("笔记查看器会话初始化完成")
    
    def login_and_sync(self, phone, password):
        """登录并同步会话"""
        self.logger.info("开始登录并同步会话")
        
        # 使用已有的登录功能
        self.login_instance = SimpleNeteaseLogin()
        success, result = self.login_instance.login_with_bypass(phone, password)
        
        if success:
            # 同步cookies
            login_cookies = dict(self.login_instance.session.cookies)
            for name, value in login_cookies.items():
                self.session.cookies.set(name, value, domain='.music.163.com')
            
            self.logger.info("登录成功，会话已同步")
            return True, result
        else:
            self.logger.error("登录失败")
            return False, None
    
    def get_user_notes(self, user_id=None):
        """获取用户笔记"""
        self.logger.info("开始获取用户笔记")
        
        # 如果没有提供user_id，先获取当前用户信息
        if not user_id:
            user_info = self.get_current_user_info()
            if user_info and 'profile' in user_info:
                user_id = user_info['profile'].get('userId')
            
            if not user_id:
                self.logger.error("无法获取用户ID")
                return None
        
        # 尝试多个可能的笔记API接口
        note_apis = [
            f'https://music.163.com/weapi/user/notes/{user_id}',
            f'https://music.163.com/api/user/notes/{user_id}',
            f'https://music.163.com/weapi/user/{user_id}/notes',
            f'https://music.163.com/api/user/{user_id}/notes',
            'https://music.163.com/weapi/user/notes',
            'https://music.163.com/api/user/notes',
            'https://music.163.com/weapi/notes/list',
            'https://music.163.com/api/notes/list'
        ]
        
        for api_url in note_apis:
            try:
                self.logger.debug(f"尝试笔记API: {api_url}")
                
                # 尝试GET请求
                response = self.session.get(api_url, timeout=10)
                self.logger.debug(f"GET响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result.get('code') == 200 and 'notes' in result:
                            self.logger.info(f"成功获取笔记: {api_url}")
                            return result['notes']
                        elif 'notes' in result or 'data' in result:
                            self.logger.info(f"可能获取到笔记数据: {api_url}")
                            return result
                    except json.JSONDecodeError:
                        pass
                
                # 尝试POST请求
                post_data = {'userId': user_id} if user_id else {}
                response = self.session.post(api_url, data=post_data, timeout=10)
                self.logger.debug(f"POST响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result.get('code') == 200 and 'notes' in result:
                            self.logger.info(f"成功获取笔记: {api_url}")
                            return result['notes']
                        elif 'notes' in result or 'data' in result:
                            self.logger.info(f"可能获取到笔记数据: {api_url}")
                            return result
                    except json.JSONDecodeError:
                        pass
                        
            except Exception as e:
                self.logger.debug(f"API {api_url} 失败: {e}")
                continue
        
        self.logger.warning("所有笔记API都失败")
        return None
    
    def get_current_user_info(self):
        """获取当前用户信息"""
        try:
            if self.login_instance:
                return self.login_instance.get_user_info()
            
            # 备用方案
            user_apis = [
                'https://music.163.com/weapi/w/nuser/account/get',
                'https://music.163.com/api/nuser/account/get',
                'https://music.163.com/weapi/user/account'
            ]
            
            for api_url in user_apis:
                try:
                    response = self.session.get(api_url, timeout=10)
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('code') == 200 or 'profile' in result:
                            return result
                except:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
    
    def search_notes_by_keyword(self, keyword):
        """根据关键词搜索笔记"""
        self.logger.info(f"搜索包含关键词的笔记: {keyword}")
        
        search_apis = [
            'https://music.163.com/weapi/search/notes',
            'https://music.163.com/api/search/notes',
            'https://music.163.com/weapi/notes/search'
        ]
        
        for api_url in search_apis:
            try:
                search_data = {
                    'keyword': keyword,
                    'type': 'note',
                    'limit': 50,
                    'offset': 0
                }
                
                response = self.session.post(api_url, data=search_data, timeout=10)
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result.get('code') == 200:
                            return result.get('result', {}).get('notes', [])
                    except json.JSONDecodeError:
                        pass
                        
            except Exception as e:
                self.logger.debug(f"搜索API {api_url} 失败: {e}")
                continue
        
        return []
    
    def get_note_detail(self, note_id):
        """获取笔记详情"""
        self.logger.info(f"获取笔记详情: {note_id}")
        
        detail_apis = [
            f'https://music.163.com/weapi/notes/{note_id}',
            f'https://music.163.com/api/notes/{note_id}',
            f'https://music.163.com/weapi/note/detail/{note_id}'
        ]
        
        for api_url in detail_apis:
            try:
                response = self.session.get(api_url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result.get('code') == 200:
                            return result.get('note', result)
                    except json.JSONDecodeError:
                        pass
                        
            except Exception as e:
                self.logger.debug(f"详情API {api_url} 失败: {e}")
                continue
        
        return None
    
    def format_notes_display(self, notes):
        """格式化笔记显示"""
        if not notes:
            return "没有找到笔记"
        
        if isinstance(notes, dict):
            # 如果是单个笔记对象
            notes = [notes]
        
        formatted_output = []
        formatted_output.append("=" * 60)
        formatted_output.append("网易云音乐笔记列表")
        formatted_output.append("=" * 60)
        
        for i, note in enumerate(notes, 1):
            formatted_output.append(f"\n📝 笔记 {i}:")
            formatted_output.append("-" * 40)
            
            # 基本信息
            if 'id' in note:
                formatted_output.append(f"ID: {note['id']}")
            if 'title' in note:
                formatted_output.append(f"标题: {note['title']}")
            if 'content' in note:
                formatted_output.append(f"内容: {note['content']}")
            if 'createTime' in note:
                create_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(note['createTime']/1000))
                formatted_output.append(f"创建时间: {create_time}")
            if 'updateTime' in note:
                update_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(note['updateTime']/1000))
                formatted_output.append(f"更新时间: {update_time}")
            
            # 关联的歌曲信息
            if 'song' in note:
                song = note['song']
                formatted_output.append(f"关联歌曲: {song.get('name', '未知')} - {song.get('artist', '未知')}")
            
            # 其他可能的字段
            for key, value in note.items():
                if key not in ['id', 'title', 'content', 'createTime', 'updateTime', 'song']:
                    formatted_output.append(f"{key}: {value}")
        
        return "\n".join(formatted_output)


def load_account_info():
    """从文件加载账号信息"""
    try:
        with open('账号密码', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            phone = lines[0].strip()
            password = lines[1].replace('密码：', '').strip()
            return phone, password
    except Exception as e:
        print(f"读取账号信息失败: {e}")
        return None, None


def main():
    """主函数"""
    print("=" * 60)
    print("网易云音乐笔记查看器")
    print("用于获取和查看网易云音乐中的笔记内容")
    print("仅用于学术研究目的")
    print("=" * 60)
    
    # 加载账号信息
    phone, password = load_account_info()
    if not phone or not password:
        print("无法读取账号信息，请检查'账号密码'文件")
        return
    
    # 创建笔记查看器
    notes_viewer = NeteaseNotesViewer()
    
    # 登录
    print(f"\n🔐 正在登录账号: {phone}")
    success, result = notes_viewer.login_and_sync(phone, password)
    
    if not success:
        print("❌ 登录失败，无法获取笔记")
        return
    
    print("✅ 登录成功")
    
    # 获取用户信息
    print("\n👤 获取用户信息...")
    user_info = notes_viewer.get_current_user_info()
    if user_info:
        print("✅ 用户信息获取成功")
        if 'profile' in user_info:
            profile = user_info['profile']
            print(f"用户昵称: {profile.get('nickname', '未知')}")
            print(f"用户ID: {profile.get('userId', '未知')}")
    else:
        print("⚠️  用户信息获取失败")
    
    # 获取笔记
    print("\n📝 正在获取笔记...")
    notes = notes_viewer.get_user_notes()
    
    if notes:
        print("✅ 笔记获取成功")
        formatted_notes = notes_viewer.format_notes_display(notes)
        print(formatted_notes)
        
        # 保存笔记到文件
        with open('我的网易云笔记.txt', 'w', encoding='utf-8') as f:
            f.write(formatted_notes)
        print(f"\n💾 笔记已保存到文件: 我的网易云笔记.txt")
        
    else:
        print("❌ 未能获取到笔记")
        print("可能的原因:")
        print("1. 账号中没有笔记")
        print("2. 需要更高权限的登录状态")
        print("3. API接口已变更")
        
        # 尝试搜索功能
        print("\n🔍 尝试搜索功能...")
        search_results = notes_viewer.search_notes_by_keyword("音乐")
        if search_results:
            print("✅ 搜索到相关笔记")
            formatted_search = notes_viewer.format_notes_display(search_results)
            print(formatted_search)
        else:
            print("❌ 搜索也未找到笔记")
    
    print(f"\n📋 详细日志请查看: netease_notes.log")


if __name__ == "__main__":
    main()
