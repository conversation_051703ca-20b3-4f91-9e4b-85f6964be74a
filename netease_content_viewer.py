"""
网易云音乐内容查看器
基于成功的API登录，查看各种内容
用于个人学术研究
"""

import requests
import json
import time
import logging
import hashlib
from urllib.parse import urlencode


class NeteaseContentViewer:
    """网易云音乐内容查看器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.logger = self._setup_logger()
        self.user_id = None
        self._setup_session()
    
    def _setup_logger(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('netease_content.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://music.163.com/',
            'Origin': 'https://music.163.com',
        }
        self.session.headers.update(headers)
        self.logger.info("内容查看器初始化完成")
    
    def login(self, phone, password):
        """登录获取权限"""
        self.logger.info("开始登录")
        
        try:
            # 使用成功的API登录方法
            login_data = {
                'phone': phone,
                'password': password,
                'rememberLogin': 'true'
            }
            
            response = self.session.post(
                'https://music.163.com/api/login/cellphone',
                data=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.logger.info("登录成功")
                
                # 尝试获取用户信息
                self._get_user_info()
                return True
            else:
                self.logger.error("登录失败")
                return False
                
        except Exception as e:
            self.logger.error(f"登录异常: {e}")
            return False
    
    def _get_user_info(self):
        """获取用户信息"""
        try:
            user_apis = [
                'https://music.163.com/api/nuser/account/get',
                'https://music.163.com/weapi/w/nuser/account/get',
                'https://music.163.com/api/user/account',
                'https://music.163.com/weapi/user/account'
            ]
            
            for api in user_apis:
                try:
                    response = self.session.get(api, timeout=10)
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if 'profile' in result:
                                profile = result['profile']
                                self.user_id = profile.get('userId')
                                self.logger.info(f"获取用户信息成功: {profile.get('nickname', '未知')}")
                                return result
                        except:
                            pass
                except:
                    continue
            
            self.logger.warning("无法获取详细用户信息")
            return None
            
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
    
    def get_user_playlists(self):
        """获取用户歌单"""
        self.logger.info("获取用户歌单")
        
        if not self.user_id:
            self.logger.warning("用户ID未知，尝试通用接口")
        
        playlist_apis = [
            f'https://music.163.com/api/user/playlist/?uid={self.user_id}&limit=1000' if self.user_id else None,
            'https://music.163.com/api/user/playlist',
            'https://music.163.com/weapi/user/playlist',
            'https://music.163.com/api/playlist/list'
        ]
        
        playlist_apis = [api for api in playlist_apis if api]  # 过滤None
        
        for api in playlist_apis:
            try:
                response = self.session.get(api, timeout=10)
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if 'playlist' in result:
                            playlists = result['playlist']
                            self.logger.info(f"获取到 {len(playlists)} 个歌单")
                            return playlists
                    except:
                        pass
            except:
                continue
        
        return []
    
    def get_user_records(self):
        """获取听歌记录"""
        self.logger.info("获取听歌记录")
        
        if not self.user_id:
            return []
        
        record_apis = [
            f'https://music.163.com/api/v1/play/record?uid={self.user_id}&type=1',
            f'https://music.163.com/weapi/v1/play/record?uid={self.user_id}&type=0',
            f'https://music.163.com/api/user/record?uid={self.user_id}'
        ]
        
        for api in record_apis:
            try:
                response = self.session.get(api, timeout=10)
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if 'weekData' in result or 'allData' in result:
                            self.logger.info("获取听歌记录成功")
                            return result
                    except:
                        pass
            except:
                continue
        
        return {}
    
    def get_user_follows(self):
        """获取关注列表"""
        self.logger.info("获取关注列表")
        
        if not self.user_id:
            return []
        
        follow_apis = [
            f'https://music.163.com/api/user/follows/{self.user_id}',
            f'https://music.163.com/weapi/user/follows/{self.user_id}'
        ]
        
        for api in follow_apis:
            try:
                response = self.session.get(api, timeout=10)
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if 'follow' in result:
                            follows = result['follow']
                            self.logger.info(f"获取到 {len(follows)} 个关注")
                            return follows
                    except:
                        pass
            except:
                continue
        
        return []
    
    def search_user_content(self, keyword):
        """搜索用户相关内容"""
        self.logger.info(f"搜索用户内容: {keyword}")
        
        search_apis = [
            'https://music.163.com/api/search/get',
            'https://music.163.com/weapi/search/get'
        ]
        
        search_types = [1, 100, 1000, 1002, 1004, 1006, 1009]  # 不同类型的搜索
        
        results = {}
        
        for api in search_apis:
            for search_type in search_types:
                try:
                    params = {
                        's': keyword,
                        'type': search_type,
                        'limit': 50,
                        'offset': 0
                    }
                    
                    response = self.session.get(f"{api}?{urlencode(params)}", timeout=10)
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if 'result' in result and result['result']:
                                results[f'type_{search_type}'] = result['result']
                        except:
                            pass
                except:
                    continue
        
        return results
    
    def get_recent_activities(self):
        """获取最近动态"""
        self.logger.info("获取最近动态")
        
        activity_apis = [
            'https://music.163.com/api/event/get',
            'https://music.163.com/weapi/event/get',
            f'https://music.163.com/api/user/event?uid={self.user_id}' if self.user_id else None
        ]
        
        activity_apis = [api for api in activity_apis if api]
        
        for api in activity_apis:
            try:
                response = self.session.get(api, timeout=10)
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if 'event' in result or 'events' in result:
                            self.logger.info("获取动态成功")
                            return result
                    except:
                        pass
            except:
                continue
        
        return {}
    
    def save_all_content(self):
        """保存所有内容到文件"""
        self.logger.info("开始保存所有内容")
        
        all_content = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'user_info': self._get_user_info(),
            'playlists': self.get_user_playlists(),
            'records': self.get_user_records(),
            'follows': self.get_user_follows(),
            'activities': self.get_recent_activities()
        }
        
        # 搜索一些可能的笔记关键词
        search_keywords = ['笔记', '日记', '心情', '感想', '记录']
        search_results = {}
        
        for keyword in search_keywords:
            results = self.search_user_content(keyword)
            if results:
                search_results[keyword] = results
        
        all_content['search_results'] = search_results
        
        # 保存到文件
        filename = f"网易云内容_{time.strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(all_content, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"内容已保存到: {filename}")
            
            # 创建可读性更好的文本版本
            text_filename = f"网易云内容_{time.strftime('%Y%m%d_%H%M%S')}.txt"
            self._create_readable_report(all_content, text_filename)
            
            return filename, text_filename
            
        except Exception as e:
            self.logger.error(f"保存文件失败: {e}")
            return None, None
    
    def _create_readable_report(self, content, filename):
        """创建可读性报告"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("网易云音乐内容报告\n")
                f.write(f"生成时间: {content['timestamp']}\n")
                f.write("=" * 60 + "\n\n")
                
                # 用户信息
                if content['user_info']:
                    f.write("👤 用户信息:\n")
                    f.write("-" * 30 + "\n")
                    user_info = content['user_info']
                    if 'profile' in user_info:
                        profile = user_info['profile']
                        f.write(f"昵称: {profile.get('nickname', '未知')}\n")
                        f.write(f"用户ID: {profile.get('userId', '未知')}\n")
                        f.write(f"签名: {profile.get('signature', '无')}\n")
                    f.write("\n")
                
                # 歌单信息
                if content['playlists']:
                    f.write(f"🎵 歌单列表 ({len(content['playlists'])} 个):\n")
                    f.write("-" * 30 + "\n")
                    for i, playlist in enumerate(content['playlists'][:10], 1):  # 只显示前10个
                        f.write(f"{i}. {playlist.get('name', '未知歌单')}\n")
                        f.write(f"   描述: {playlist.get('description', '无描述')}\n")
                        f.write(f"   歌曲数: {playlist.get('trackCount', 0)}\n\n")
                
                # 搜索结果
                if content['search_results']:
                    f.write("🔍 搜索结果:\n")
                    f.write("-" * 30 + "\n")
                    for keyword, results in content['search_results'].items():
                        f.write(f"关键词: {keyword}\n")
                        for result_type, data in results.items():
                            f.write(f"  类型 {result_type}: 找到 {len(data) if isinstance(data, list) else '未知'} 条结果\n")
                        f.write("\n")
                
                f.write("=" * 60 + "\n")
                f.write("详细数据请查看对应的JSON文件\n")
                
        except Exception as e:
            self.logger.error(f"创建可读性报告失败: {e}")


def load_account_info():
    """从文件加载账号信息"""
    try:
        with open('账号密码', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            phone = lines[0].strip()
            password = lines[1].replace('密码：', '').strip()
            return phone, password
    except Exception as e:
        print(f"读取账号信息失败: {e}")
        return None, None


def main():
    """主函数"""
    print("=" * 60)
    print("网易云音乐内容查看器")
    print("基于API登录查看各种内容")
    print("用于个人学术研究")
    print("=" * 60)
    
    # 加载账号信息
    phone, password = load_account_info()
    if not phone or not password:
        print("无法读取账号信息，请检查'账号密码'文件")
        return
    
    # 创建内容查看器
    viewer = NeteaseContentViewer()
    
    # 登录
    print(f"\n🔐 正在登录账号: {phone}")
    if not viewer.login(phone, password):
        print("❌ 登录失败")
        return
    
    print("✅ 登录成功")
    
    # 获取并保存所有内容
    print("\n📋 正在获取所有可访问的内容...")
    json_file, text_file = viewer.save_all_content()
    
    if json_file and text_file:
        print(f"\n✅ 内容获取完成!")
        print(f"📁 详细数据: {json_file}")
        print(f"📄 可读报告: {text_file}")
        print("\n🔍 内容包括:")
        print("- 用户基本信息")
        print("- 歌单列表")
        print("- 听歌记录")
        print("- 关注列表")
        print("- 最近动态")
        print("- 搜索结果（可能包含笔记）")
    else:
        print("❌ 内容获取失败")
    
    print(f"\n📋 详细日志请查看: netease_content.log")


if __name__ == "__main__":
    main()
