"""
网易云音乐加密工具模块
用于处理登录过程中的加密和解密操作
"""

import json
import base64
import binascii
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
import hashlib
import random
import string


class NeteaseEncrypt:
    """网易云音乐加密工具类"""
    
    def __init__(self):
        self.modulus = '00e0b509f6259df8642dbc35662901477df22677ec152b5ff68ace615bb7b725152b3ab17a876aea8a5aa76d2e417629ec4ee341f56135fccf695280104e0312ecbda92557c93870114af6c9d05c4f7f0c3685b7a46bee255932575cce10b424d813cfe4875d3e82047b97ddef52741d546b8e289dc6935b3ece0462db0a22b8e7'
        self.nonce = '0CoJUm6Qyw8W8jud'
        self.pub_key = '010001'
    
    def create_secret_key(self, size=16):
        """生成随机密钥"""
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(size))
    
    def aes_encrypt(self, text, sec_key):
        """AES加密"""
        pad = 16 - len(text) % 16
        text = text + chr(pad) * pad
        encryptor = AES.new(sec_key.encode('utf-8'), AES.MODE_CBC, b'0102030405060708')
        ciphertext = encryptor.encrypt(text.encode('utf-8'))
        return base64.b64encode(ciphertext).decode('utf-8')
    
    def rsa_encrypt(self, text, pub_key, modulus):
        """RSA加密"""
        text = text[::-1]
        rs = pow(int(binascii.hexlify(text.encode('utf-8')), 16), int(pub_key, 16), int(modulus, 16))
        return format(rs, 'x').zfill(256)
    
    def encrypt_login_data(self, data):
        """加密登录数据"""
        text = json.dumps(data)
        sec_key = self.create_secret_key()
        
        # 第一次AES加密
        enc_text = self.aes_encrypt(text, self.nonce)
        # 第二次AES加密
        enc_text = self.aes_encrypt(enc_text, sec_key)
        
        # RSA加密密钥
        enc_sec_key = self.rsa_encrypt(sec_key, self.pub_key, self.modulus)
        
        return {
            'params': enc_text,
            'encSecKey': enc_sec_key
        }
    
    def generate_device_id(self):
        """生成设备ID"""
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(32))
    
    def md5_hash(self, text):
        """MD5哈希"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def generate_csrf_token(self):
        """生成CSRF令牌"""
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(32))


def create_login_params(phone, password, captcha='', captcha_id=''):
    """创建登录参数"""
    encrypt = NeteaseEncrypt()
    
    data = {
        'phone': phone,
        'password': hashlib.md5(password.encode('utf-8')).hexdigest(),
        'rememberLogin': 'true'
    }
    
    if captcha:
        data['captcha'] = captcha
        data['captchaId'] = captcha_id
    
    return encrypt.encrypt_login_data(data)


def generate_fake_fingerprint():
    """生成虚假设备指纹"""
    return {
        'os': 'Windows',
        'browser': 'Chrome',
        'version': '120.0.0.0',
        'language': 'zh-CN',
        'platform': 'Win32',
        'timezone': -480,
        'screen': '1920x1080',
        'deviceId': NeteaseEncrypt().generate_device_id()
    }
