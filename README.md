# 网易云音乐登录绕过验证研究程序

## 项目说明

本项目是一个用于学术研究目的的网易云音乐登录验证绕过程序。该程序实现了多种技术方案来研究和分析网易云音乐的登录验证机制。

**⚠️ 重要声明：本项目仅用于学术研究和技术学习，请勿用于任何商业或恶意目的。使用本程序时请遵守相关法律法规和平台服务条款。**

## 功能特性

### 🔐 多种绕过方法
1. **直接API登录** - 绕过前端验证，直接调用后端API
2. **浏览器自动化** - 使用Selenium模拟真实用户操作
3. **会话劫持** - 模拟正常浏览器行为建立会话

### 🤖 智能验证码处理
- 文字验证码自动识别（基于ddddocr）
- 滑块验证码自动解决
- 图像预处理和增强
- 人类行为模拟

### 🛡️ 反检测机制
- 随机User-Agent生成
- 设备指纹伪造
- 请求时序模拟
- 浏览器特征隐藏

## 技术架构

```
网易云绕登录/
├── netease_login.py          # 主程序入口
├── config.py                 # 配置文件
├── requirements.txt          # 依赖包列表
├── 账号密码                   # 账号信息文件
├── utils/                    # 工具模块
│   ├── crypto_utils.py       # 加密工具
│   ├── captcha_solver.py     # 验证码识别
│   └── browser_automation.py # 浏览器自动化
└── README.md                 # 项目说明
```

## 安装和使用

### 1. 环境要求
- Python 3.7+
- Chrome浏览器
- 稳定的网络连接

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置账号信息
编辑 `账号密码` 文件，格式如下：
```
手机号
密码：你的密码
```

### 4. 运行程序
```bash
python netease_login.py
```

## 核心技术

### 加密算法
- **AES加密** - 用于参数加密
- **RSA加密** - 用于密钥加密
- **MD5哈希** - 用于密码处理

### 验证码识别
- **ddddocr** - 高精度OCR识别
- **OpenCV** - 图像处理和模板匹配
- **PIL** - 图像预处理

### 浏览器自动化
- **Selenium** - 浏览器控制
- **ActionChains** - 鼠标操作模拟
- **WebDriverWait** - 智能等待

## 配置选项

### 基本配置 (config.py)
```python
# 网易云音乐API地址
NETEASE_URLS = {
    'login_url': 'https://music.163.com/weapi/login/cellphone',
    # ... 其他URL
}

# 请求头配置
DEFAULT_HEADERS = {
    'User-Agent': '...',
    # ... 其他请求头
}
```

### 验证码配置
```python
CAPTCHA_CONFIG = {
    'ddddocr_beta': True,      # 使用beta版本
    'retry_times': 3,          # 重试次数
    'timeout': 10              # 超时时间
}
```

### 浏览器配置
```python
SELENIUM_CONFIG = {
    'headless': True,          # 无头模式
    'window_size': (1920, 1080),
    'timeout': 30
}
```

## 研究方法说明

### 方法1: 直接API登录
- 分析网易云登录API接口
- 实现参数加密算法
- 绕过前端验证直接调用后端
- 适用于无验证码或验证码失效的情况

### 方法2: 浏览器自动化
- 使用Selenium控制真实浏览器
- 自动处理各种验证码
- 模拟人类操作行为
- 成功率最高但速度较慢

### 方法3: 会话劫持
- 模拟正常浏览器建立会话
- 伪造浏览器特征和行为
- 绕过基于会话的检测
- 平衡速度和成功率

## 日志和调试

程序会生成详细的日志文件 `netease_research.log`，包含：
- 每个方法的执行过程
- 验证码识别结果
- 网络请求详情
- 错误和异常信息

## 注意事项

1. **合法使用** - 仅用于学术研究，不得用于非法目的
2. **频率控制** - 避免过于频繁的请求，防止IP被封
3. **账号安全** - 使用测试账号，避免主账号风险
4. **及时更新** - 网易云可能更新验证机制，需要相应调整

## 技术原理

### 登录流程分析
1. 用户访问登录页面
2. 输入手机号和密码
3. 系统可能要求验证码验证
4. 提交加密后的登录数据
5. 服务器验证并返回结果

### 验证绕过原理
1. **参数加密** - 逆向分析加密算法
2. **请求伪造** - 模拟正常客户端请求
3. **验证码处理** - 自动识别和输入
4. **行为模拟** - 模拟人类操作特征

## 扩展功能

程序支持以下扩展：
- 添加新的验证码识别算法
- 实现更多反检测机制
- 支持批量账号测试
- 集成代理池功能

## 故障排除

### 常见问题
1. **ChromeDriver错误** - 自动下载最新版本
2. **验证码识别失败** - 检查ddddocr安装
3. **网络连接问题** - 检查代理设置
4. **登录失败** - 检查账号密码正确性

### 调试建议
1. 查看日志文件了解详细错误
2. 开启浏览器可视模式调试
3. 检查网络连接和代理设置
4. 更新依赖包到最新版本

## 免责声明

本项目仅供学习和研究使用，开发者不对使用本程序造成的任何后果负责。使用者应当：

1. 遵守当地法律法规
2. 尊重平台服务条款
3. 不进行恶意攻击或滥用
4. 承担使用风险和责任

---

**如有技术问题或改进建议，欢迎提交Issue或Pull Request。**
