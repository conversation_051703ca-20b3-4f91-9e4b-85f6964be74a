
// 网易云音乐笔记提取脚本
// 在网易云音乐网页版的控制台中运行此脚本

console.log("🎯 开始提取网易云音乐笔记...");

function extractNotes() {
    const noteSelectors = [
        '.note-item', '.diary-item', '.dynamic-item', '.event-item',
        '.content', '.text-content', '.note-content', '.diary-content',
        '.comment-content', '.user-content', '.dynamic-content',
        '[data-type="note"]', '[data-type="diary"]', '[data-type="dynamic"]'
    ];
    
    let allNotes = [];
    let noteIndex = 1;
    
    noteSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
            const text = el.innerText?.trim();
            if (text && text.length > 10) {
                // 过滤掉明显不是笔记的内容
                if (!text.includes('http') && !text.includes('www') && 
                    !text.includes('登录') && !text.includes('注册')) {
                    allNotes.push({
                        index: noteIndex++,
                        selector: selector,
                        content: text,
                        html: el.outerHTML.substring(0, 200) + '...'
                    });
                }
            }
        });
    });
    
    console.log(`找到 ${allNotes.length} 条可能的笔记内容:`);
    allNotes.forEach(note => {
        console.log(`笔记 ${note.index}: ${note.content.substring(0, 100)}...`);
    });
    
    // 将结果复制到剪贴板
    const result = allNotes.map(note => 
        `笔记 ${note.index}:\n${note.content}\n${'='.repeat(50)}`
    ).join('\n\n');
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(result).then(() => {
            console.log("✅ 笔记内容已复制到剪贴板！");
            alert(`找到 ${allNotes.length} 条笔记，已复制到剪贴板！`);
        });
    } else {
        console.log("⚠️  无法自动复制，请手动复制以下内容:");
        console.log(result);
    }
    
    return allNotes;
}

// 执行提取
extractNotes();

// 提供手动搜索功能
function searchNotes(keyword) {
    console.log(`🔍 搜索关键词: ${keyword}`);
    const allText = document.body.innerText;
    const lines = allText.split('\n');
    const matches = lines.filter(line => 
        line.includes(keyword) && line.trim().length > 10
    );
    
    console.log(`找到 ${matches.length} 条包含"${keyword}"的内容:`);
    matches.forEach((match, i) => {
        console.log(`${i+1}: ${match.trim()}`);
    });
    
    return matches;
}

console.log("💡 提示: 可以使用 searchNotes('关键词') 搜索特定内容");
console.log("例如: searchNotes('笔记') 或 searchNotes('心情')");
