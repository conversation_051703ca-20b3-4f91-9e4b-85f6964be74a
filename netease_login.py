"""
网易云音乐登录绕过验证主程序
用于学术研究目的
"""

import requests
import json
import time
import logging
from fake_useragent import UserAgent
from utils.crypto_utils import create_login_params, generate_fake_fingerprint
from utils.captcha_solver import CaptchaSolver
from utils.browser_automation import Browser<PERSON>utomation
from config import *


class NeteaseLoginBypass:
    """网易云音乐登录绕过类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.captcha_solver = CaptchaSolver()
        self.browser = None
        self.logger = self._setup_logger()
        self._setup_session()
    
    def _setup_logger(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, LOG_CONFIG['level']),
            format=LOG_CONFIG['format'],
            handlers=[
                logging.FileHandler(LOG_CONFIG['file'], encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_session(self):
        """设置会话"""
        # 设置请求头
        ua = UserAgent()
        headers = DEFAULT_HEADERS.copy()
        headers['User-Agent'] = ua.random
        self.session.headers.update(headers)
        
        # 设置代理（如果启用）
        if PROXY_CONFIG['enabled']:
            self.session.proxies.update({
                'http': PROXY_CONFIG['http'],
                'https': PROXY_CONFIG['https']
            })
        
        self.logger.info("会话初始化完成")
    
    def method1_direct_api_login(self, phone, password):
        """方法1: 直接API登录（绕过前端验证）"""
        self.logger.info("尝试方法1: 直接API登录")
        
        try:
            # 创建加密参数
            login_data = create_login_params(phone, password)
            
            # 添加额外参数
            fingerprint = generate_fake_fingerprint()
            
            # 发送登录请求
            response = self.session.post(
                NETEASE_URLS['login_url'],
                data=login_data,
                timeout=10
            )
            
            result = response.json()
            
            if result.get('code') == 200:
                self.logger.info("方法1成功: 直接API登录成功")
                return True, result
            else:
                self.logger.warning(f"方法1失败: {result.get('message', '未知错误')}")
                return False, result
                
        except Exception as e:
            self.logger.error(f"方法1异常: {e}")
            return False, None
    
    def method2_browser_automation(self, phone, password):
        """方法2: 浏览器自动化登录"""
        self.logger.info("尝试方法2: 浏览器自动化登录")
        
        try:
            self.browser = BrowserAutomation(headless=SELENIUM_CONFIG['headless'])
            
            # 导航到登录页面
            if not self.browser.navigate_to_login():
                return False, None
            
            # 输入凭据
            if not self.browser.input_credentials(phone, password):
                return False, None
            
            # 处理验证码
            if not self.browser.handle_captcha(self.captcha_solver):
                self.logger.warning("验证码处理失败，尝试继续")
            
            # 点击登录
            if not self.browser.click_login():
                return False, None
            
            # 检查登录结果
            time.sleep(3)
            if self.browser.check_login_success():
                cookies = self.browser.get_cookies()
                self.logger.info("方法2成功: 浏览器自动化登录成功")
                return True, {'cookies': cookies}
            else:
                self.logger.warning("方法2失败: 登录未成功")
                return False, None
                
        except Exception as e:
            self.logger.error(f"方法2异常: {e}")
            return False, None
        finally:
            if self.browser:
                self.browser.close()
    
    def method3_session_hijacking(self, phone, password):
        """方法3: 会话劫持（模拟正常浏览器行为）"""
        self.logger.info("尝试方法3: 会话劫持")
        
        try:
            # 首先访问主页建立会话
            self.session.get(NETEASE_URLS['main_url'])
            time.sleep(1)
            
            # 获取登录页面
            login_page = self.session.get('https://music.163.com/#/login')
            
            # 模拟浏览器行为
            self._simulate_browser_behavior()
            
            # 尝试无验证码登录
            login_data = create_login_params(phone, password)
            
            # 添加更多伪造信息
            headers = {
                'Referer': 'https://music.163.com/',
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }
            
            response = self.session.post(
                NETEASE_URLS['login_url'],
                data=login_data,
                headers=headers,
                timeout=10
            )
            
            result = response.json()
            
            if result.get('code') == 200:
                self.logger.info("方法3成功: 会话劫持登录成功")
                return True, result
            else:
                self.logger.warning(f"方法3失败: {result.get('message', '未知错误')}")
                return False, result
                
        except Exception as e:
            self.logger.error(f"方法3异常: {e}")
            return False, None
    
    def _simulate_browser_behavior(self):
        """模拟浏览器行为"""
        try:
            # 模拟加载资源
            resources = [
                'https://music.163.com/style/web2/index.css',
                'https://music.163.com/script/web2/index.js'
            ]
            
            for resource in resources:
                try:
                    self.session.get(resource, timeout=5)
                    time.sleep(0.1)
                except:
                    pass
            
            # 随机延迟
            time.sleep(random.uniform(1, 3))
            
        except Exception as e:
            self.logger.warning(f"模拟浏览器行为失败: {e}")
    
    def login_with_bypass(self, phone, password):
        """使用多种方法尝试绕过验证登录"""
        self.logger.info(f"开始尝试登录账号: {phone}")
        
        methods = [
            self.method1_direct_api_login,
            self.method3_session_hijacking,
            self.method2_browser_automation
        ]
        
        for i, method in enumerate(methods, 1):
            try:
                success, result = method(phone, password)
                if success:
                    self.logger.info(f"登录成功！使用方法{i}")
                    return True, result
                else:
                    self.logger.info(f"方法{i}失败，尝试下一个方法")
                    time.sleep(2)  # 避免请求过快
            except Exception as e:
                self.logger.error(f"方法{i}执行异常: {e}")
                continue
        
        self.logger.error("所有登录方法都失败了")
        return False, None
    
    def get_user_info(self):
        """获取用户信息"""
        try:
            response = self.session.get(NETEASE_URLS['user_info_url'])
            return response.json()
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None


def load_account_info():
    """从文件加载账号信息"""
    try:
        with open('账号密码', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            phone = lines[0].strip()
            password = lines[1].replace('密码：', '').strip()
            return phone, password
    except Exception as e:
        print(f"读取账号信息失败: {e}")
        return None, None


def main():
    """主函数"""
    print("=" * 50)
    print("网易云音乐登录绕过验证研究程序")
    print("仅用于学术研究目的")
    print("=" * 50)
    
    # 加载账号信息
    phone, password = load_account_info()
    if not phone or not password:
        print("无法读取账号信息，请检查'账号密码'文件")
        return
    
    # 创建登录绕过实例
    bypass = NeteaseLoginBypass()
    
    # 尝试登录
    success, result = bypass.login_with_bypass(phone, password)
    
    if success:
        print("\n✅ 登录成功！")
        print("登录结果:", json.dumps(result, indent=2, ensure_ascii=False))
        
        # 获取用户信息
        user_info = bypass.get_user_info()
        if user_info:
            print("\n用户信息:", json.dumps(user_info, indent=2, ensure_ascii=False))
    else:
        print("\n❌ 登录失败！")
        print("所有绕过方法都未成功")


if __name__ == "__main__":
    main()
