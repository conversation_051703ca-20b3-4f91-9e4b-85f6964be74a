"""
网易云音乐笔记查看器 - 浏览器版本
使用浏览器自动化获取完整的登录状态和笔记内容
用于学术研究目的
"""

import requests
import json
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import os


class NeteaseNotesBrowser:
    """网易云音乐笔记浏览器查看器"""
    
    def __init__(self):
        self.driver = None
        self.wait = None
        self.session = requests.Session()
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('netease_notes_browser.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def setup_browser(self, headless=False):
        """设置浏览器"""
        try:
            chrome_options = Options()
            
            if headless:
                chrome_options.add_argument('--headless')
            
            # 基本选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 反检测选项
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 尝试使用本地ChromeDriver
            try:
                self.driver = webdriver.Chrome(options=chrome_options)
            except:
                # 如果本地没有，尝试自动下载
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 30)
            self.logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def manual_login_and_extract(self, phone, password):
        """手动登录并提取笔记"""
        if not self.setup_browser(headless=False):
            return False
        
        try:
            print("\n🌐 正在打开网易云音乐...")
            self.driver.get('https://music.163.com')
            time.sleep(3)
            
            print("📱 请在浏览器中手动完成以下操作:")
            print("1. 点击登录按钮")
            print("2. 输入手机号和密码")
            print("3. 完成任何验证码验证")
            print("4. 登录成功后，程序将自动继续...")
            
            # 等待用户手动登录
            print("\n⏳ 等待登录完成（最多5分钟）...")
            login_success = False
            
            for i in range(300):  # 等待5分钟
                try:
                    # 检查是否已登录（查找用户头像或用户名）
                    user_elements = self.driver.find_elements(By.CSS_SELECTOR, '.topbar .user, .user .name, .avatar')
                    if user_elements:
                        login_success = True
                        break
                    
                    # 检查URL是否变化
                    current_url = self.driver.current_url
                    if 'discover' in current_url or 'my' in current_url:
                        login_success = True
                        break
                        
                except:
                    pass
                
                time.sleep(1)
                if i % 30 == 0:  # 每30秒提示一次
                    print(f"⏳ 仍在等待登录... ({i//30 + 1}/10)")
            
            if not login_success:
                print("❌ 登录超时，请重新运行程序")
                return False
            
            print("✅ 检测到登录成功！")
            
            # 尝试访问笔记相关页面
            print("\n📝 正在查找笔记...")
            
            # 尝试访问个人主页
            try:
                self.driver.get('https://music.163.com/#/user/home')
                time.sleep(3)
            except:
                pass
            
            # 尝试查找笔记相关的元素
            note_selectors = [
                '.note', '.notes', '.diary', '.comment',
                '[data-type="note"]', '[data-tab="note"]',
                '.user-note', '.my-note', '.note-item'
            ]
            
            found_notes = []
            
            for selector in note_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"✅ 找到 {len(elements)} 个可能的笔记元素: {selector}")
                        for element in elements:
                            try:
                                text = element.text.strip()
                                if text and len(text) > 10:  # 过滤掉太短的文本
                                    found_notes.append({
                                        'selector': selector,
                                        'text': text,
                                        'html': element.get_attribute('outerHTML')[:500]
                                    })
                            except:
                                pass
                except:
                    continue
            
            # 尝试通过JavaScript获取页面数据
            print("\n🔍 尝试提取页面数据...")
            try:
                # 获取页面中的所有文本内容
                page_text = self.driver.execute_script("return document.body.innerText;")
                
                # 查找可能的笔记内容
                lines = page_text.split('\n')
                potential_notes = []
                
                for line in lines:
                    line = line.strip()
                    # 查找可能是笔记的文本（长度适中，包含中文）
                    if (20 <= len(line) <= 500 and 
                        any('\u4e00' <= char <= '\u9fff' for char in line) and
                        not line.startswith(('http', 'www', '©', '版权'))):
                        potential_notes.append(line)
                
                if potential_notes:
                    print(f"✅ 找到 {len(potential_notes)} 条可能的笔记内容")
                    found_notes.extend([{'type': 'text', 'content': note} for note in potential_notes])
                
            except Exception as e:
                self.logger.debug(f"JavaScript提取失败: {e}")
            
            # 尝试访问具体的笔记页面
            note_urls = [
                'https://music.163.com/#/user/notes',
                'https://music.163.com/#/my/notes',
                'https://music.163.com/#/notes',
                'https://music.163.com/#/user/diary'
            ]
            
            for url in note_urls:
                try:
                    print(f"🔗 尝试访问: {url}")
                    self.driver.get(url)
                    time.sleep(3)
                    
                    # 检查页面内容
                    page_source = self.driver.page_source
                    if '笔记' in page_source or 'note' in page_source.lower():
                        print(f"✅ 在 {url} 找到笔记相关内容")
                        
                        # 尝试提取笔记
                        note_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                            '.note-item, .diary-item, .content, .text, .comment-content')
                        
                        for element in note_elements:
                            try:
                                text = element.text.strip()
                                if text and len(text) > 10:
                                    found_notes.append({
                                        'url': url,
                                        'content': text
                                    })
                            except:
                                pass
                
                except Exception as e:
                    self.logger.debug(f"访问 {url} 失败: {e}")
                    continue
            
            # 保存结果
            if found_notes:
                self.save_notes_to_file(found_notes)
                print(f"\n✅ 总共找到 {len(found_notes)} 条可能的笔记内容")
                print("📁 内容已保存到 '网易云笔记提取结果.txt'")
                return True
            else:
                print("\n❌ 未找到任何笔记内容")
                print("可能的原因:")
                print("1. 账号中确实没有笔记")
                print("2. 笔记功能的页面结构已变更")
                print("3. 需要特定的权限或操作")
                return False
                
        except Exception as e:
            self.logger.error(f"提取笔记过程出错: {e}")
            return False
        finally:
            if self.driver:
                print("\n⏳ 浏览器将在10秒后关闭，您可以手动查看...")
                time.sleep(10)
                self.driver.quit()
    
    def save_notes_to_file(self, notes):
        """保存笔记到文件"""
        try:
            with open('网易云笔记提取结果.txt', 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("网易云音乐笔记提取结果\n")
                f.write(f"提取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 60 + "\n\n")
                
                for i, note in enumerate(notes, 1):
                    f.write(f"📝 内容 {i}:\n")
                    f.write("-" * 40 + "\n")
                    
                    if 'content' in note:
                        f.write(f"内容: {note['content']}\n")
                    if 'text' in note:
                        f.write(f"文本: {note['text']}\n")
                    if 'url' in note:
                        f.write(f"来源: {note['url']}\n")
                    if 'selector' in note:
                        f.write(f"选择器: {note['selector']}\n")
                    
                    f.write("\n" + "=" * 40 + "\n\n")
                
                f.write(f"\n总计: {len(notes)} 条内容\n")
                
        except Exception as e:
            self.logger.error(f"保存文件失败: {e}")


def load_account_info():
    """从文件加载账号信息"""
    try:
        with open('账号密码', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            phone = lines[0].strip()
            password = lines[1].replace('密码：', '').strip()
            return phone, password
    except Exception as e:
        print(f"读取账号信息失败: {e}")
        return None, None


def main():
    """主函数"""
    print("=" * 60)
    print("网易云音乐笔记查看器 - 浏览器版本")
    print("使用浏览器自动化获取笔记内容")
    print("仅用于学术研究目的")
    print("=" * 60)
    
    # 加载账号信息
    phone, password = load_account_info()
    if not phone or not password:
        print("无法读取账号信息，请检查'账号密码'文件")
        return
    
    print(f"\n📱 准备登录账号: {phone}")
    print("⚠️  注意: 此程序需要您手动完成登录过程")
    print("程序会打开浏览器，请在浏览器中完成登录")
    
    input("\n按回车键继续...")
    
    # 创建笔记查看器
    notes_browser = NeteaseNotesBrowser()
    
    # 开始提取
    success = notes_browser.manual_login_and_extract(phone, password)
    
    if success:
        print("\n🎉 笔记提取完成！")
        print("请查看生成的文件了解详细内容")
    else:
        print("\n😔 笔记提取未成功")
        print("您可以:")
        print("1. 重新运行程序")
        print("2. 检查账号是否有笔记内容")
        print("3. 手动在浏览器中查看笔记")
    
    print(f"\n📋 详细日志请查看: netease_notes_browser.log")


if __name__ == "__main__":
    main()
