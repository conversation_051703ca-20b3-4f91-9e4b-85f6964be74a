"""
网易云音乐登录绕过验证程序 - 简化版
仅使用基础库，避免复杂依赖问题
用于学术研究目的
"""

import requests
import json
import time
import random
import string
import hashlib
import base64
import binascii
import logging
from urllib.parse import urlencode


class SimpleNeteaseLogin:
    """简化版网易云登录类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.logger = self._setup_logger()
        self._setup_session()
    
    def _setup_logger(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('netease_simple.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://music.163.com/',
            'Origin': 'https://music.163.com',
        }
        self.session.headers.update(headers)
        self.logger.info("会话初始化完成")
    
    def create_secret_key(self, size=16):
        """生成随机密钥"""
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(size))
    
    def aes_encrypt(self, text, key):
        """简化的AES加密（使用base64模拟）"""
        try:
            from Crypto.Cipher import AES
            pad = 16 - len(text) % 16
            text = text + chr(pad) * pad
            encryptor = AES.new(key.encode('utf-8'), AES.MODE_CBC, b'0102030405060708')
            ciphertext = encryptor.encrypt(text.encode('utf-8'))
            return base64.b64encode(ciphertext).decode('utf-8')
        except ImportError:
            # 如果没有Crypto库，使用简单的base64编码
            self.logger.warning("Crypto库未安装，使用简化加密")
            combined = f"{text}:{key}"
            return base64.b64encode(combined.encode('utf-8')).decode('utf-8')
    
    def rsa_encrypt(self, text, pub_key, modulus):
        """RSA加密"""
        try:
            text = text[::-1]
            rs = pow(int(binascii.hexlify(text.encode('utf-8')), 16), int(pub_key, 16), int(modulus, 16))
            return format(rs, 'x').zfill(256)
        except Exception as e:
            self.logger.warning(f"RSA加密失败，使用备用方案: {e}")
            return hashlib.md5(text.encode('utf-8')).hexdigest() * 8  # 256位
    
    def encrypt_login_data(self, data):
        """加密登录数据"""
        text = json.dumps(data)
        nonce = '0CoJUm6Qyw8W8jud'
        pub_key = '010001'
        modulus = '00e0b509f6259df8642dbc35662901477df22677ec152b5ff68ace615bb7b725152b3ab17a876aea8a5aa76d2e417629ec4ee341f56135fccf695280104e0312ecbda92557c93870114af6c9d05c4f7f0c3685b7a46bee255932575cce10b424d813cfe4875d3e82047b97ddef52741d546b8e289dc6935b3ece0462db0a22b8e7'
        
        sec_key = self.create_secret_key()
        
        # 第一次AES加密
        enc_text = self.aes_encrypt(text, nonce)
        # 第二次AES加密
        enc_text = self.aes_encrypt(enc_text, sec_key)
        
        # RSA加密密钥
        enc_sec_key = self.rsa_encrypt(sec_key, pub_key, modulus)
        
        return {
            'params': enc_text,
            'encSecKey': enc_sec_key
        }
    
    def method1_direct_login(self, phone, password):
        """方法1: 直接登录"""
        self.logger.info("尝试方法1: 直接API登录")
        
        try:
            # 准备登录数据
            data = {
                'phone': phone,
                'password': hashlib.md5(password.encode('utf-8')).hexdigest(),
                'rememberLogin': 'true'
            }
            
            # 加密数据
            encrypted_data = self.encrypt_login_data(data)
            
            # 发送请求
            url = 'https://music.163.com/weapi/login/cellphone'
            response = self.session.post(url, data=encrypted_data, timeout=10)
            
            result = response.json()
            
            if result.get('code') == 200:
                self.logger.info("方法1成功: 直接登录成功")
                return True, result
            else:
                self.logger.warning(f"方法1失败: {result.get('message', '未知错误')}")
                return False, result
                
        except Exception as e:
            self.logger.error(f"方法1异常: {e}")
            return False, None
    
    def method2_simple_session(self, phone, password):
        """方法2: 简单会话登录"""
        self.logger.info("尝试方法2: 简单会话登录")
        
        try:
            # 先访问主页
            self.session.get('https://music.163.com')
            time.sleep(1)
            
            # 模拟登录页面访问
            self.session.get('https://music.163.com/#/login')
            time.sleep(1)
            
            # 准备登录数据（使用简化格式）
            login_data = {
                'phone': phone,
                'password': hashlib.md5(password.encode('utf-8')).hexdigest(),
                'rememberLogin': 'true',
                'csrf_token': self._generate_csrf_token()
            }
            
            # 尝试不同的登录接口
            urls = [
                'https://music.163.com/weapi/login/cellphone',
                'https://music.163.com/api/login/cellphone',
                'https://music.163.com/weapi/w/login/cellphone'
            ]
            
            for url in urls:
                try:
                    # 加密数据
                    encrypted_data = self.encrypt_login_data(login_data)
                    
                    response = self.session.post(url, data=encrypted_data, timeout=10)
                    result = response.json()
                    
                    if result.get('code') == 200:
                        self.logger.info(f"方法2成功: 使用接口 {url}")
                        return True, result
                    else:
                        self.logger.debug(f"接口 {url} 失败: {result.get('message', '未知错误')}")
                        
                except Exception as e:
                    self.logger.debug(f"接口 {url} 异常: {e}")
                    continue
            
            self.logger.warning("方法2失败: 所有接口都失败")
            return False, None
            
        except Exception as e:
            self.logger.error(f"方法2异常: {e}")
            return False, None
    
    def method3_form_login(self, phone, password):
        """方法3: 表单登录"""
        self.logger.info("尝试方法3: 表单登录")
        
        try:
            # 使用简单的表单数据
            form_data = {
                'phone': phone,
                'password': password,
                'rememberLogin': 'true'
            }
            
            # 添加表单头
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            response = self.session.post(
                'https://music.163.com/api/login/cellphone',
                data=urlencode(form_data),
                headers=headers,
                timeout=10
            )
            
            # 尝试解析响应
            try:
                result = response.json()
            except:
                # 如果不是JSON，检查是否重定向成功
                if response.status_code == 200 and 'music.163.com' in response.url:
                    self.logger.info("方法3成功: 可能登录成功（非JSON响应）")
                    return True, {'message': '登录可能成功', 'cookies': dict(self.session.cookies)}
                else:
                    self.logger.warning(f"方法3失败: HTTP {response.status_code}")
                    return False, None
            
            if result.get('code') == 200:
                self.logger.info("方法3成功: 表单登录成功")
                return True, result
            else:
                self.logger.warning(f"方法3失败: {result.get('message', '未知错误')}")
                return False, result
                
        except Exception as e:
            self.logger.error(f"方法3异常: {e}")
            return False, None
    
    def _generate_csrf_token(self):
        """生成CSRF令牌"""
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(32))
    
    def login_with_bypass(self, phone, password):
        """尝试多种方法登录"""
        self.logger.info(f"开始尝试登录账号: {phone}")
        
        methods = [
            self.method1_direct_login,
            self.method2_simple_session,
            self.method3_form_login
        ]
        
        for i, method in enumerate(methods, 1):
            try:
                success, result = method(phone, password)
                if success:
                    self.logger.info(f"登录成功！使用方法{i}")
                    return True, result
                else:
                    self.logger.info(f"方法{i}失败，尝试下一个方法")
                    time.sleep(2)
            except Exception as e:
                self.logger.error(f"方法{i}执行异常: {e}")
                continue
        
        self.logger.error("所有登录方法都失败了")
        return False, None
    
    def get_user_info(self):
        """获取用户信息"""
        try:
            response = self.session.get('https://music.163.com/weapi/w/nuser/account/get')
            return response.json()
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None


def load_account_info():
    """从文件加载账号信息"""
    try:
        with open('账号密码', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            phone = lines[0].strip()
            password = lines[1].replace('密码：', '').strip()
            return phone, password
    except Exception as e:
        print(f"读取账号信息失败: {e}")
        return None, None


def main():
    """主函数"""
    print("=" * 50)
    print("网易云音乐登录绕过验证研究程序 - 简化版")
    print("仅用于学术研究目的")
    print("=" * 50)
    
    # 加载账号信息
    phone, password = load_account_info()
    if not phone or not password:
        print("无法读取账号信息，请检查'账号密码'文件")
        return
    
    # 创建登录实例
    login = SimpleNeteaseLogin()
    
    # 尝试登录
    success, result = login.login_with_bypass(phone, password)
    
    if success:
        print("\n✅ 登录成功！")
        if result:
            print("登录结果:", json.dumps(result, indent=2, ensure_ascii=False))
        
        # 尝试获取用户信息
        user_info = login.get_user_info()
        if user_info:
            print("\n用户信息:", json.dumps(user_info, indent=2, ensure_ascii=False))
    else:
        print("\n❌ 登录失败！")
        print("所有方法都未成功")
    
    print(f"\n详细日志请查看: netease_simple.log")


if __name__ == "__main__":
    main()
